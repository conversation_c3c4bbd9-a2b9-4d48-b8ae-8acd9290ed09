package io.kt666.mcp.calcmcp.integration;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.reactive.AutoConfigureWebTestClient;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.reactive.server.WebTestClient;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * MCP协议集成测试
 * 测试完整的MCP请求-响应流程
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureWebTestClient
class McpProtocolIntegrationTest {

    @Autowired
    private WebTestClient webTestClient;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    void testToolDiscovery_ShouldReturnAllAvailableTools() {
        webTestClient.get()
                .uri("/mcp/tools")
                .accept(MediaType.APPLICATION_JSON)
                .exchange()
                .expectStatus().isOk()
                .expectBody()
                .jsonPath("$.success").isEqualTo(true)
                .jsonPath("$.tools").exists()
                .jsonPath("$.tools.calculator_add").exists()
                .jsonPath("$.tools.calculator_subtract").exists()
                .jsonPath("$.tools.calculator_multiply").exists()
                .jsonPath("$.tools.calculator_divide").exists()
                .jsonPath("$.server").exists()
                .jsonPath("$.toolCount").isEqualTo(4)
                .jsonPath("$.timestamp").exists();
    }

    @Test
    void testGetSpecificToolDefinition_ShouldReturnToolDetails() {
        webTestClient.get()
                .uri("/mcp/tools/calculator_add")
                .accept(MediaType.APPLICATION_JSON)
                .exchange()
                .expectStatus().isOk()
                .expectBody()
                .jsonPath("$.success").isEqualTo(true)
                .jsonPath("$.toolName").isEqualTo("calculator_add")
                .jsonPath("$.tool.name").isEqualTo("calculator_add")
                .jsonPath("$.tool.description").exists()
                .jsonPath("$.tool.inputSchema").exists()
                .jsonPath("$.tool.inputSchema.type").isEqualTo("object")
                .jsonPath("$.tool.inputSchema.properties.numbers").exists()
                .jsonPath("$.timestamp").exists();
    }

    @Test
    void testGetNonExistentTool_ShouldReturn404() {
        webTestClient.get()
                .uri("/mcp/tools/non_existent_tool")
                .accept(MediaType.APPLICATION_JSON)
                .exchange()
                .expectStatus().isNotFound();
    }

    @Test
    void testServerInfo_ShouldReturnServerDetails() {
        webTestClient.get()
                .uri("/mcp/server/info")
                .accept(MediaType.APPLICATION_JSON)
                .exchange()
                .expectStatus().isOk()
                .expectBody()
                .jsonPath("$.success").isEqualTo(true)
                .jsonPath("$.server.name").isEqualTo("Calculator MCP Server")
                .jsonPath("$.server.version").isEqualTo("1.0.0")
                .jsonPath("$.server.description").exists()
                .jsonPath("$.timestamp").exists();
    }

    @Test
    void testHealthCheck_ShouldReturnServiceStatus() {
        webTestClient.get()
                .uri("/mcp/health")
                .accept(MediaType.APPLICATION_JSON)
                .exchange()
                .expectStatus().isOk()
                .expectBody()
                .jsonPath("$.success").isEqualTo(true)
                .jsonPath("$.status").isEqualTo("UP")
                .jsonPath("$.service").isEqualTo("Calculator MCP Server")
                .jsonPath("$.availableTools").isArray()
                .jsonPath("$.toolCount").isEqualTo(4)
                .jsonPath("$.timestamp").exists();
    }

    @Test
    void testAddToolCall_ValidInput_ShouldReturnCorrectResult() {
        Map<String, Object> request = createToolCallRequest(Arrays.asList(5.0, 3.0, 2.0));

        webTestClient.post()
                .uri("/mcp/tools/calculator_add/call")
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(request)
                .exchange()
                .expectStatus().isOk()
                .expectBody()
                .jsonPath("$.success").isEqualTo(true)
                .jsonPath("$.result").isEqualTo(10.0)
                .jsonPath("$.operation").isEqualTo("add")
                .jsonPath("$.operands").isArray()
                .jsonPath("$.description").exists()
                .jsonPath("$.timestamp").exists();
    }

    @Test
    void testSubtractToolCall_ValidInput_ShouldReturnCorrectResult() {
        Map<String, Object> request = createToolCallRequest(Arrays.asList(10.0, 3.0, 2.0));

        webTestClient.post()
                .uri("/mcp/tools/calculator_subtract/call")
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(request)
                .exchange()
                .expectStatus().isOk()
                .expectBody()
                .jsonPath("$.success").isEqualTo(true)
                .jsonPath("$.result").isEqualTo(5.0)
                .jsonPath("$.operation").isEqualTo("subtract")
                .jsonPath("$.operands").isArray()
                .jsonPath("$.description").exists();
    }

    @Test
    void testMultiplyToolCall_ValidInput_ShouldReturnCorrectResult() {
        Map<String, Object> request = createToolCallRequest(Arrays.asList(2.0, 3.0, 4.0));

        webTestClient.post()
                .uri("/mcp/tools/calculator_multiply/call")
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(request)
                .exchange()
                .expectStatus().isOk()
                .expectBody()
                .jsonPath("$.success").isEqualTo(true)
                .jsonPath("$.result").isEqualTo(24.0)
                .jsonPath("$.operation").isEqualTo("multiply")
                .jsonPath("$.operands").isArray()
                .jsonPath("$.description").exists();
    }

    @Test
    void testDivideToolCall_ValidInput_ShouldReturnCorrectResult() {
        Map<String, Object> request = createToolCallRequest(Arrays.asList(20.0, 2.0, 2.0));

        webTestClient.post()
                .uri("/mcp/tools/calculator_divide/call")
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(request)
                .exchange()
                .expectStatus().isOk()
                .expectBody()
                .jsonPath("$.success").isEqualTo(true)
                .jsonPath("$.result").isEqualTo(5.0)
                .jsonPath("$.operation").isEqualTo("divide")
                .jsonPath("$.operands").isArray()
                .jsonPath("$.description").exists();
    }

    @Test
    void testToolCall_NonExistentTool_ShouldReturnError() {
        Map<String, Object> request = createToolCallRequest(Arrays.asList(5.0, 3.0));

        webTestClient.post()
                .uri("/mcp/tools/non_existent_tool/call")
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(request)
                .exchange()
                .expectStatus().isBadRequest()
                .expectBody()
                .jsonPath("$.success").isEqualTo(false)
                .jsonPath("$.error.code").isEqualTo("REQUEST_ERROR")
                .jsonPath("$.error.message").exists()
                .jsonPath("$.error.data.operation").isEqualTo("tool_call");
    }

    @Test
    void testToolCall_InvalidInput_MissingNumbers_ShouldReturnError() {
        Map<String, Object> request = new HashMap<>();
        // 故意不包含 "numbers" 参数

        webTestClient.post()
                .uri("/mcp/tools/calculator_add/call")
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(request)
                .exchange()
                .expectStatus().isBadRequest()
                .expectBody()
                .jsonPath("$.success").isEqualTo(false)
                .jsonPath("$.error.code").isEqualTo("REQUEST_ERROR")
                .jsonPath("$.error.message").value(org.hamcrest.Matchers.containsString("Missing required parameter"));
    }

    @Test
    void testToolCall_InvalidInput_InsufficientNumbers_ShouldReturnError() {
        Map<String, Object> request = createToolCallRequest(Arrays.asList(5.0)); // 只有一个数字

        webTestClient.post()
                .uri("/mcp/tools/calculator_add/call")
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(request)
                .exchange()
                .expectStatus().isBadRequest()
                .expectBody()
                .jsonPath("$.success").isEqualTo(false)
                .jsonPath("$.error.code").exists()
                .jsonPath("$.error.message").value(org.hamcrest.Matchers.containsString("At least 2 numbers are required"));
    }

    @Test
    void testDivideToolCall_DivisionByZero_ShouldReturnError() {
        Map<String, Object> request = createToolCallRequest(Arrays.asList(10.0, 0.0));

        webTestClient.post()
                .uri("/mcp/tools/calculator_divide/call")
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(request)
                .exchange()
                .expectStatus().isBadRequest()
                .expectBody()
                .jsonPath("$.success").isEqualTo(false)
                .jsonPath("$.error.code").isEqualTo("DIVISION_BY_ZERO")
                .jsonPath("$.error.message").isEqualTo("Division by zero is not allowed");
    }

    @Test
    void testToolCall_InvalidInput_NonNumericValues_ShouldReturnError() {
        Map<String, Object> request = new HashMap<>();
        request.put("numbers", Arrays.asList("not_a_number", 3.0));

        webTestClient.post()
                .uri("/mcp/tools/calculator_add/call")
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(request)
                .exchange()
                .expectStatus().isBadRequest()
                .expectBody()
                .jsonPath("$.success").isEqualTo(false)
                .jsonPath("$.error.code").isEqualTo("REQUEST_ERROR")
                .jsonPath("$.error.message").value(org.hamcrest.Matchers.containsString("must be numeric"));
    }

    @Test
    void testToolCall_WithDecimalNumbers_ShouldHandleCorrectly() {
        Map<String, Object> request = createToolCallRequest(Arrays.asList(1.5, 2.7, 0.8));

        webTestClient.post()
                .uri("/mcp/tools/calculator_add/call")
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(request)
                .exchange()
                .expectStatus().isOk()
                .expectBody()
                .jsonPath("$.success").isEqualTo(true)
                .jsonPath("$.result").isEqualTo(5.0)
                .jsonPath("$.operation").isEqualTo("add");
    }

    @Test
    void testToolCall_WithNegativeNumbers_ShouldHandleCorrectly() {
        Map<String, Object> request = createToolCallRequest(Arrays.asList(-5.0, 3.0, -2.0));

        webTestClient.post()
                .uri("/mcp/tools/calculator_add/call")
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(request)
                .exchange()
                .expectStatus().isOk()
                .expectBody()
                .jsonPath("$.success").isEqualTo(true)
                .jsonPath("$.result").isEqualTo(-4.0)
                .jsonPath("$.operation").isEqualTo("add");
    }

    @Test
    void testCompleteWorkflow_ToolDiscoveryToExecution() {
        // 1. 首先发现可用工具
        webTestClient.get()
                .uri("/mcp/tools")
                .exchange()
                .expectStatus().isOk()
                .expectBody()
                .jsonPath("$.success").isEqualTo(true)
                .jsonPath("$.tools.calculator_add").exists();

        // 2. 获取特定工具定义
        webTestClient.get()
                .uri("/mcp/tools/calculator_add")
                .exchange()
                .expectStatus().isOk()
                .expectBody()
                .jsonPath("$.success").isEqualTo(true)
                .jsonPath("$.tool.name").isEqualTo("calculator_add");

        // 3. 执行工具调用
        Map<String, Object> request = createToolCallRequest(Arrays.asList(10.0, 5.0));
        webTestClient.post()
                .uri("/mcp/tools/calculator_add/call")
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(request)
                .exchange()
                .expectStatus().isOk()
                .expectBody()
                .jsonPath("$.success").isEqualTo(true)
                .jsonPath("$.result").isEqualTo(15.0);
    }

    /**
     * 创建工具调用请求
     */
    private Map<String, Object> createToolCallRequest(List<Double> numbers) {
        Map<String, Object> request = new HashMap<>();
        request.put("numbers", numbers);
        return request;
    }
}