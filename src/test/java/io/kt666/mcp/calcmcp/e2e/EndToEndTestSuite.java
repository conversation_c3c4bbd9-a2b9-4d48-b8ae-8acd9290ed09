package io.kt666.mcp.calcmcp.e2e;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.reactive.AutoConfigureWebTestClient;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.reactive.server.WebTestClient;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 完整的端到端测试套件
 * 模拟Kiro IDE调用，测试所有四种数学运算的完整流程
 * 验证错误处理和异常情况的正确响应
 * 确保所有需求的验收标准都得到满足
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureWebTestClient
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
class EndToEndTestSuite {

    @Autowired
    private WebTestClient webTestClient;

    @Autowired
    private ObjectMapper objectMapper;

    private static final String BASE_URL = "/mcp";
    private static final String TOOLS_URL = BASE_URL + "/tools";
    private static final String HEALTH_URL = BASE_URL + "/health";
    private static final String SERVER_INFO_URL = BASE_URL + "/server/info";

    /**
     * 测试1：服务启动和健康检查
     * 验证需求6.1：MCP服务启动时，系统应该正确注册所有计算工具
     */
    @Test
    @Order(1)
    @DisplayName("E2E-001: 服务启动和健康检查")
    void testServiceStartupAndHealth() {
        // 验证健康检查端点
        webTestClient.get()
                .uri(HEALTH_URL)
                .accept(MediaType.APPLICATION_JSON)
                .exchange()
                .expectStatus().isOk()
                .expectBody()
                .jsonPath("$.success").isEqualTo(true)
                .jsonPath("$.status").isEqualTo("UP")
                .jsonPath("$.service").isEqualTo("Calculator MCP Server")
                .jsonPath("$.availableTools").isArray()
                .jsonPath("$.toolCount").isNumber()
                .jsonPath("$.timestamp").exists();

        System.out.println("✅ E2E-001: 服务启动和健康检查 - 通过");
    }

    /**
     * 测试2：工具发现和注册验证
     * 验证需求6.1：MCP服务启动时，系统应该正确注册所有计算工具
     */
    @Test
    @Order(2)
    @DisplayName("E2E-002: 工具发现和注册验证")
    void testToolDiscoveryAndRegistration() {
        webTestClient.get()
                .uri(TOOLS_URL)
                .accept(MediaType.APPLICATION_JSON)
                .exchange()
                .expectStatus().isOk()
                .expectBody()
                .jsonPath("$.success").isEqualTo(true)
                .jsonPath("$.tools").exists()
                .jsonPath("$.tools.calculator_add").exists()
                .jsonPath("$.tools.calculator_subtract").exists()
                .jsonPath("$.tools.calculator_multiply").exists()
                .jsonPath("$.tools.calculator_divide").exists()
                .jsonPath("$.server").exists()
                .jsonPath("$.toolCount").isEqualTo(4);

        // 验证每个工具的详细信息
        String[] toolNames = {"calculator_add", "calculator_subtract", "calculator_multiply", "calculator_divide"};
        for (String toolName : toolNames) {
            webTestClient.get()
                    .uri(TOOLS_URL + "/" + toolName)
                    .accept(MediaType.APPLICATION_JSON)
                    .exchange()
                    .expectStatus().isOk()
                    .expectBody()
                    .jsonPath("$.success").isEqualTo(true)
                    .jsonPath("$.toolName").isEqualTo(toolName)
                    .jsonPath("$.tool.name").isEqualTo(toolName)
                    .jsonPath("$.tool.description").exists()
                    .jsonPath("$.tool.inputSchema").exists();
        }

        System.out.println("✅ E2E-002: 工具发现和注册验证 - 通过");
    }

    /**
     * 测试3：加法运算完整流程
     * 验证需求1的所有验收标准
     */
    @Test
    @Order(3)
    @DisplayName("E2E-003: 加法运算完整流程")
    void testAdditionCompleteWorkflow() {
        // 需求1.1：两个数字的和
        testCalculation("calculator_add", Arrays.asList(5.0, 3.0), 8.0, "add");
        
        // 需求1.2：多个数字的总和
        testCalculation("calculator_add", Arrays.asList(1.0, 2.0, 3.0, 4.0), 10.0, "add");
        
        // 需求1.3：小数运算
        testCalculation("calculator_add", Arrays.asList(1.5, 2.7, 0.8), 5.0, "add");
        
        // 需求1.4：负数加法
        testCalculation("calculator_add", Arrays.asList(-5.0, 3.0, -2.0), -4.0, "add");

        System.out.println("✅ E2E-003: 加法运算完整流程 - 通过");
    }

    /**
     * 测试4：减法运算完整流程
     * 验证需求2的所有验收标准
     */
    @Test
    @Order(4)
    @DisplayName("E2E-004: 减法运算完整流程")
    void testSubtractionCompleteWorkflow() {
        // 需求2.1：两个数字的差
        testCalculation("calculator_subtract", Arrays.asList(10.0, 3.0), 7.0, "subtract");
        
        // 需求2.2：多个数字按顺序减法
        testCalculation("calculator_subtract", Arrays.asList(20.0, 5.0, 3.0, 2.0), 10.0, "subtract");
        
        // 需求2.3：负数结果
        testCalculation("calculator_subtract", Arrays.asList(5.0, 10.0), -5.0, "subtract");
        
        // 需求2.4：小数减法
        testCalculation("calculator_subtract", Arrays.asList(10.5, 3.2, 1.3), 6.0, "subtract");

        System.out.println("✅ E2E-004: 减法运算完整流程 - 通过");
    }

    /**
     * 测试5：乘法运算完整流程
     * 验证需求3的所有验收标准
     */
    @Test
    @Order(5)
    @DisplayName("E2E-005: 乘法运算完整流程")
    void testMultiplicationCompleteWorkflow() {
        // 需求3.1：两个数字的乘积
        testCalculation("calculator_multiply", Arrays.asList(4.0, 5.0), 20.0, "multiply");
        
        // 需求3.2：多个数字的乘积
        testCalculation("calculator_multiply", Arrays.asList(2.0, 3.0, 4.0), 24.0, "multiply");
        
        // 需求3.3：零值处理
        testCalculation("calculator_multiply", Arrays.asList(5.0, 0.0, 3.0), 0.0, "multiply");
        
        // 需求3.4：小数乘法
        testCalculation("calculator_multiply", Arrays.asList(2.5, 4.0), 10.0, "multiply");

        System.out.println("✅ E2E-005: 乘法运算完整流程 - 通过");
    }

    /**
     * 测试6：除法运算完整流程
     * 验证需求4的所有验收标准
     */
    @Test
    @Order(6)
    @DisplayName("E2E-006: 除法运算完整流程")
    void testDivisionCompleteWorkflow() {
        // 需求4.1：两个数字的商
        testCalculation("calculator_divide", Arrays.asList(20.0, 4.0), 5.0, "divide");
        
        // 需求4.3：小数结果
        testCalculation("calculator_divide", Arrays.asList(7.0, 2.0), 3.5, "divide");
        
        // 需求4.4：多个数字按顺序除法
        testCalculation("calculator_divide", Arrays.asList(24.0, 2.0, 3.0), 4.0, "divide");
        
        // 零除以数字
        testCalculation("calculator_divide", Arrays.asList(0.0, 5.0), 0.0, "divide");

        System.out.println("✅ E2E-006: 除法运算完整流程 - 通过");
    }

    /**
     * 测试7：输入验证和错误处理
     * 验证需求5的所有验收标准
     */
    @Test
    @Order(7)
    @DisplayName("E2E-007: 输入验证和错误处理")
    void testInputValidationAndErrorHandling() {
        // 需求5.2：参数不足
        testErrorScenario("calculator_add", Arrays.asList(5.0), "At least 2 numbers are required");
        
        // 需求5.1：无效数字（通过非数字字符串测试）
        Map<String, Object> invalidRequest = new HashMap<>();
        invalidRequest.put("numbers", Arrays.asList("not_a_number", 3.0));
        
        webTestClient.post()
                .uri(TOOLS_URL + "/calculator_add/call")
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(invalidRequest)
                .exchange()
                .expectStatus().isBadRequest()
                .expectBody()
                .jsonPath("$.success").isEqualTo(false)
                .jsonPath("$.error").exists();
        
        // 需求5.3：除零错误
        testDivisionByZeroError();
        
        // 需求5.2：缺少参数
        Map<String, Object> emptyRequest = new HashMap<>();
        webTestClient.post()
                .uri(TOOLS_URL + "/calculator_add/call")
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(emptyRequest)
                .exchange()
                .expectStatus().isBadRequest()
                .expectBody()
                .jsonPath("$.success").isEqualTo(false)
                .jsonPath("$.error").exists();

        System.out.println("✅ E2E-007: 输入验证和错误处理 - 通过");
    }

    /**
     * 测试8：MCP协议响应格式验证
     * 验证需求6.2：用户调用计算工具时，系统应该返回符合MCP协议的响应格式
     */
    @Test
    @Order(8)
    @DisplayName("E2E-008: MCP协议响应格式验证")
    void testMcpProtocolResponseFormat() {
        Map<String, Object> request = createToolCallRequest(Arrays.asList(10.0, 5.0));
        
        webTestClient.post()
                .uri(TOOLS_URL + "/calculator_add/call")
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(request)
                .exchange()
                .expectStatus().isOk()
                .expectBody()
                .jsonPath("$.success").isEqualTo(true)
                .jsonPath("$.result").isNumber()
                .jsonPath("$.operation").isEqualTo("add")
                .jsonPath("$.operands").isArray()
                .jsonPath("$.description").exists()
                .jsonPath("$.timestamp").exists();

        System.out.println("✅ E2E-008: MCP协议响应格式验证 - 通过");
    }

    /**
     * 测试9：并发请求处理
     * 验证需求6.4：服务运行时，系统应该能够处理并发的计算请求
     */
    @Test
    @Order(9)
    @DisplayName("E2E-009: 并发请求处理")
    void testConcurrentRequestHandling() {
        // 创建多个并发请求
        Map<String, Object> request1 = createToolCallRequest(Arrays.asList(10.0, 5.0));
        Map<String, Object> request2 = createToolCallRequest(Arrays.asList(20.0, 4.0));
        Map<String, Object> request3 = createToolCallRequest(Arrays.asList(15.0, 3.0));

        // 同时发送多个请求
        webTestClient.post()
                .uri(TOOLS_URL + "/calculator_add/call")
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(request1)
                .exchange()
                .expectStatus().isOk()
                .expectBody()
                .jsonPath("$.success").isEqualTo(true)
                .jsonPath("$.result").isEqualTo(15.0);

        webTestClient.post()
                .uri(TOOLS_URL + "/calculator_divide/call")
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(request2)
                .exchange()
                .expectStatus().isOk()
                .expectBody()
                .jsonPath("$.success").isEqualTo(true)
                .jsonPath("$.result").isEqualTo(5.0);

        webTestClient.post()
                .uri(TOOLS_URL + "/calculator_multiply/call")
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(request3)
                .exchange()
                .expectStatus().isOk()
                .expectBody()
                .jsonPath("$.success").isEqualTo(true)
                .jsonPath("$.result").isEqualTo(45.0);

        System.out.println("✅ E2E-009: 并发请求处理 - 通过");
    }

    /**
     * 测试10：完整的Kiro IDE集成模拟
     * 模拟完整的Kiro IDE工作流程
     */
    @Test
    @Order(10)
    @DisplayName("E2E-010: 完整的Kiro IDE集成模拟")
    void testCompleteKiroIdeIntegration() {
        // 步骤1：Kiro IDE启动时发现可用工具
        webTestClient.get()
                .uri(TOOLS_URL)
                .accept(MediaType.APPLICATION_JSON)
                .exchange()
                .expectStatus().isOk()
                .expectBody()
                .jsonPath("$.success").isEqualTo(true)
                .jsonPath("$.tools").exists();

        // 步骤2：获取特定工具的详细信息
        webTestClient.get()
                .uri(TOOLS_URL + "/calculator_add")
                .accept(MediaType.APPLICATION_JSON)
                .exchange()
                .expectStatus().isOk()
                .expectBody()
                .jsonPath("$.success").isEqualTo(true)
                .jsonPath("$.tool.inputSchema").exists();

        // 步骤3：执行一系列计算操作（模拟用户交互）
        // 计算：(10 + 5) * 3 / 2 - 1 = 21.5
        
        // 第一步：10 + 5 = 15
        Map<String, Object> addResult = executeCalculationAndGetResult("calculator_add", Arrays.asList(10.0, 5.0));
        assertEquals(15.0, (Double) addResult.get("result"), 0.001);
        
        // 第二步：15 * 3 = 45
        Map<String, Object> multiplyResult = executeCalculationAndGetResult("calculator_multiply", Arrays.asList(15.0, 3.0));
        assertEquals(45.0, (Double) multiplyResult.get("result"), 0.001);
        
        // 第三步：45 / 2 = 22.5
        Map<String, Object> divideResult = executeCalculationAndGetResult("calculator_divide", Arrays.asList(45.0, 2.0));
        assertEquals(22.5, (Double) divideResult.get("result"), 0.001);
        
        // 第四步：22.5 - 1 = 21.5
        Map<String, Object> subtractResult = executeCalculationAndGetResult("calculator_subtract", Arrays.asList(22.5, 1.0));
        assertEquals(21.5, (Double) subtractResult.get("result"), 0.001);

        // 步骤4：验证服务状态仍然健康
        webTestClient.get()
                .uri(HEALTH_URL)
                .accept(MediaType.APPLICATION_JSON)
                .exchange()
                .expectStatus().isOk()
                .expectBody()
                .jsonPath("$.success").isEqualTo(true)
                .jsonPath("$.status").isEqualTo("UP");

        System.out.println("✅ E2E-010: 完整的Kiro IDE集成模拟 - 通过");
        System.out.println("   计算结果：(10 + 5) * 3 / 2 - 1 = 21.5");
    }

    /**
     * 测试11：错误恢复和服务稳定性
     * 验证需求6.3：服务出现错误时，系统应该返回符合MCP错误格式的响应
     */
    @Test
    @Order(11)
    @DisplayName("E2E-011: 错误恢复和服务稳定性")
    void testErrorRecoveryAndServiceStability() {
        // 故意触发错误
        testDivisionByZeroError();
        
        // 验证服务仍然可以处理正常请求
        testCalculation("calculator_add", Arrays.asList(5.0, 3.0), 8.0, "add");
        
        // 再次触发不同类型的错误
        testErrorScenario("calculator_multiply", Arrays.asList(1.0), "At least 2 numbers are required");
        
        // 验证服务仍然健康
        webTestClient.get()
                .uri(HEALTH_URL)
                .accept(MediaType.APPLICATION_JSON)
                .exchange()
                .expectStatus().isOk()
                .expectBody()
                .jsonPath("$.success").isEqualTo(true)
                .jsonPath("$.status").isEqualTo("UP");

        System.out.println("✅ E2E-011: 错误恢复和服务稳定性 - 通过");
    }

    /**
     * 测试12：最终验证和清理
     * 确保所有需求的验收标准都得到满足
     */
    @Test
    @Order(12)
    @DisplayName("E2E-012: 最终验证和清理")
    void testFinalValidationAndCleanup() {
        // 验证服务器信息
        webTestClient.get()
                .uri(SERVER_INFO_URL)
                .accept(MediaType.APPLICATION_JSON)
                .exchange()
                .expectStatus().isOk()
                .expectBody()
                .jsonPath("$.success").isEqualTo(true)
                .jsonPath("$.server.name").isEqualTo("Calculator MCP Server")
                .jsonPath("$.server.version").isEqualTo("1.0.0");

        // 最终健康检查
        webTestClient.get()
                .uri(HEALTH_URL)
                .accept(MediaType.APPLICATION_JSON)
                .exchange()
                .expectStatus().isOk()
                .expectBody()
                .jsonPath("$.success").isEqualTo(true)
                .jsonPath("$.status").isEqualTo("UP");

        System.out.println("✅ E2E-012: 最终验证和清理 - 通过");
        System.out.println("🎉 所有端到端测试完成！Calculator MCP Server 完全符合需求规范。");
    }

    // 辅助方法

    private void testCalculation(String toolName, List<Double> numbers, Double expectedResult, String expectedOperation) {
        Map<String, Object> request = createToolCallRequest(numbers);
        
        webTestClient.post()
                .uri(TOOLS_URL + "/" + toolName + "/call")
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(request)
                .exchange()
                .expectStatus().isOk()
                .expectBody()
                .jsonPath("$.success").isEqualTo(true)
                .jsonPath("$.result").isEqualTo(expectedResult)
                .jsonPath("$.operation").isEqualTo(expectedOperation)
                .jsonPath("$.operands").isArray()
                .jsonPath("$.description").exists()
                .jsonPath("$.timestamp").exists();
    }

    private void testErrorScenario(String toolName, List<Double> numbers, String expectedErrorMessage) {
        Map<String, Object> request = createToolCallRequest(numbers);
        
        webTestClient.post()
                .uri(TOOLS_URL + "/" + toolName + "/call")
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(request)
                .exchange()
                .expectStatus().isBadRequest()
                .expectBody()
                .jsonPath("$.success").isEqualTo(false)
                .jsonPath("$.error").exists();
    }

    private void testDivisionByZeroError() {
        Map<String, Object> request = createToolCallRequest(Arrays.asList(10.0, 0.0));
        
        webTestClient.post()
                .uri(TOOLS_URL + "/calculator_divide/call")
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(request)
                .exchange()
                .expectStatus().isBadRequest()
                .expectBody()
                .jsonPath("$.success").isEqualTo(false)
                .jsonPath("$.error").exists();
    }

    @SuppressWarnings("unchecked")
    private Map<String, Object> executeCalculationAndGetResult(String toolName, List<Double> numbers) {
        Map<String, Object> request = createToolCallRequest(numbers);
        
        return webTestClient.post()
                .uri(TOOLS_URL + "/" + toolName + "/call")
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(request)
                .exchange()
                .expectStatus().isOk()
                .expectBody(Map.class)
                .returnResult()
                .getResponseBody();
    }

    private Map<String, Object> createToolCallRequest(List<Double> numbers) {
        Map<String, Object> request = new HashMap<>();
        request.put("numbers", numbers);
        return request;
    }
}