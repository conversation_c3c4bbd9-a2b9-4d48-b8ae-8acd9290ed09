package io.kt666.mcp.calcmcp.service.impl;

import io.kt666.mcp.calcmcp.exception.DivisionByZeroException;
import io.kt666.mcp.calcmcp.exception.InvalidInputException;
import io.kt666.mcp.calcmcp.service.InputValidator;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class InputValidatorImplTest {

    private InputValidator inputValidator;

    @BeforeEach
    void setUp() {
        inputValidator = new InputValidatorImpl();
    }

    @Test
    void testValidateNumbers_ValidInput() {
        List<Double> validNumbers = Arrays.asList(1.0, 2.0, 3.0);
        assertDoesNotThrow(() -> inputValidator.validateNumbers(validNumbers));
    }

    @Test
    void testValidateNumbers_NullList() {
        InvalidInputException exception = assertThrows(
            InvalidInputException.class,
            () -> inputValidator.validateNumbers(null)
        );
        assertEquals("Numbers list cannot be null", exception.getMessage());
        assertEquals("INVALID_INPUT", exception.getErrorCode());
    }

    @Test
    void testValidateNumbers_EmptyList() {
        List<Double> emptyList = Collections.emptyList();
        InvalidInputException exception = assertThrows(
            InvalidInputException.class,
            () -> inputValidator.validateNumbers(emptyList)
        );
        assertEquals("Numbers list cannot be empty", exception.getMessage());
    }

    @Test
    void testValidateNumbers_InsufficientNumbers() {
        List<Double> singleNumber = Collections.singletonList(1.0);
        InvalidInputException exception = assertThrows(
            InvalidInputException.class,
            () -> inputValidator.validateNumbers(singleNumber)
        );
        assertTrue(exception.getMessage().contains("At least 2 numbers are required"));
    }

    @Test
    void testValidateNumbers_ContainsNull() {
        List<Double> numbersWithNull = Arrays.asList(1.0, null, 3.0);
        InvalidInputException exception = assertThrows(
            InvalidInputException.class,
            () -> inputValidator.validateNumbers(numbersWithNull)
        );
        assertTrue(exception.getMessage().contains("Number at position 1 is null"));
    }

    @Test
    void testValidateNumbers_ContainsNaN() {
        List<Double> numbersWithNaN = Arrays.asList(1.0, Double.NaN, 3.0);
        InvalidInputException exception = assertThrows(
            InvalidInputException.class,
            () -> inputValidator.validateNumbers(numbersWithNaN)
        );
        assertTrue(exception.getMessage().contains("Number at position 1 is NaN"));
    }

    @Test
    void testValidateNumbers_ContainsInfinity() {
        List<Double> numbersWithInfinity = Arrays.asList(1.0, Double.POSITIVE_INFINITY, 3.0);
        InvalidInputException exception = assertThrows(
            InvalidInputException.class,
            () -> inputValidator.validateNumbers(numbersWithInfinity)
        );
        assertTrue(exception.getMessage().contains("Number at position 1 is infinite"));
    }

    @Test
    void testValidateNotEmpty_ValidInput() {
        List<Double> validNumbers = Arrays.asList(1.0, 2.0);
        assertDoesNotThrow(() -> inputValidator.validateNotEmpty(validNumbers));
    }

    @Test
    void testValidateDivisionByZero_ValidInput() {
        List<Double> validNumbers = Arrays.asList(10.0, 2.0, 5.0);
        assertDoesNotThrow(() -> inputValidator.validateDivisionByZero(validNumbers));
    }

    @Test
    void testValidateDivisionByZero_ZeroDivisor() {
        List<Double> numbersWithZero = Arrays.asList(10.0, 0.0);
        DivisionByZeroException exception = assertThrows(
            DivisionByZeroException.class,
            () -> inputValidator.validateDivisionByZero(numbersWithZero)
        );
        assertEquals("Division by zero is not allowed", exception.getMessage());
        assertEquals("DIVISION_BY_ZERO", exception.getErrorCode());
    }

    @Test
    void testValidateDivisionByZero_MultipleZeroDivisors() {
        List<Double> numbersWithZeros = Arrays.asList(10.0, 2.0, 0.0, 5.0);
        assertThrows(
            DivisionByZeroException.class,
            () -> inputValidator.validateDivisionByZero(numbersWithZeros)
        );
    }

    @Test
    void testValidateDivisionByZero_FirstNumberCanBeZero() {
        List<Double> numbersWithFirstZero = Arrays.asList(0.0, 2.0, 5.0);
        assertDoesNotThrow(() -> inputValidator.validateDivisionByZero(numbersWithFirstZero));
    }

    @Test
    void testValidateNumberRange_ValidNumbers() {
        List<Double> validNumbers = Arrays.asList(-100.5, 0.0, 100.5);
        assertDoesNotThrow(() -> inputValidator.validateNumberRange(validNumbers));
    }

    @Test
    void testValidateNumberRange_NullList() {
        assertDoesNotThrow(() -> inputValidator.validateNumberRange(null));
    }

    @Test
    void testValidateNumberRange_NegativeInfinity() {
        List<Double> numbersWithNegInfinity = Arrays.asList(1.0, Double.NEGATIVE_INFINITY);
        InvalidInputException exception = assertThrows(
            InvalidInputException.class,
            () -> inputValidator.validateNumberRange(numbersWithNegInfinity)
        );
        assertTrue(exception.getMessage().contains("Number at position 1 is infinite"));
    }
}