package io.kt666.mcp.calcmcp.service.impl;

import io.kt666.mcp.calcmcp.exception.DivisionByZeroException;
import io.kt666.mcp.calcmcp.exception.InvalidInputException;
import io.kt666.mcp.calcmcp.model.CalculationResponse;
import io.kt666.mcp.calcmcp.service.CalculatorService;
import io.kt666.mcp.calcmcp.service.InputValidator;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class CalculatorServiceImplTest {

    private CalculatorService calculatorService;
    private InputValidator inputValidator;

    @BeforeEach
    void setUp() {
        inputValidator = new InputValidatorImpl();
        calculatorService = new CalculatorServiceImpl(inputValidator);
    }

    // 加法测试
    @Test
    void testAdd_TwoPositiveNumbers() {
        List<Double> numbers = Arrays.asList(5.0, 3.0);
        CalculationResponse response = calculatorService.add(numbers);
        
        assertEquals(8.0, response.getResult(), 0.001);
        assertEquals("add", response.getOperation());
        assertEquals(numbers, response.getOperands());
        assertTrue(response.getDescription().contains("Added 2 numbers"));
    }

    @Test
    void testAdd_MultipleNumbers() {
        List<Double> numbers = Arrays.asList(1.0, 2.0, 3.0, 4.0);
        CalculationResponse response = calculatorService.add(numbers);
        
        assertEquals(10.0, response.getResult(), 0.001);
        assertEquals("add", response.getOperation());
        assertTrue(response.getDescription().contains("Added 4 numbers"));
    }

    @Test
    void testAdd_WithNegativeNumbers() {
        List<Double> numbers = Arrays.asList(-5.0, 3.0, -2.0);
        CalculationResponse response = calculatorService.add(numbers);
        
        assertEquals(-4.0, response.getResult(), 0.001);
        assertEquals("add", response.getOperation());
    }

    @Test
    void testAdd_WithDecimals() {
        List<Double> numbers = Arrays.asList(1.5, 2.7, 0.8);
        CalculationResponse response = calculatorService.add(numbers);
        
        assertEquals(5.0, response.getResult(), 0.001);
        assertEquals("add", response.getOperation());
    }

    @Test
    void testAdd_WithZero() {
        List<Double> numbers = Arrays.asList(0.0, 5.0, 0.0);
        CalculationResponse response = calculatorService.add(numbers);
        
        assertEquals(5.0, response.getResult(), 0.001);
    }

    // 减法测试
    @Test
    void testSubtract_TwoNumbers() {
        List<Double> numbers = Arrays.asList(10.0, 3.0);
        CalculationResponse response = calculatorService.subtract(numbers);
        
        assertEquals(7.0, response.getResult(), 0.001);
        assertEquals("subtract", response.getOperation());
        assertEquals(numbers, response.getOperands());
    }

    @Test
    void testSubtract_MultipleNumbers() {
        List<Double> numbers = Arrays.asList(20.0, 5.0, 3.0, 2.0);
        CalculationResponse response = calculatorService.subtract(numbers);
        
        assertEquals(10.0, response.getResult(), 0.001); // 20 - 5 - 3 - 2 = 10
        assertEquals("subtract", response.getOperation());
    }

    @Test
    void testSubtract_ResultingInNegative() {
        List<Double> numbers = Arrays.asList(5.0, 10.0);
        CalculationResponse response = calculatorService.subtract(numbers);
        
        assertEquals(-5.0, response.getResult(), 0.001);
        assertEquals("subtract", response.getOperation());
    }

    @Test
    void testSubtract_WithDecimals() {
        List<Double> numbers = Arrays.asList(10.5, 3.2, 1.3);
        CalculationResponse response = calculatorService.subtract(numbers);
        
        assertEquals(6.0, response.getResult(), 0.001); // 10.5 - 3.2 - 1.3 = 6.0
        assertEquals("subtract", response.getOperation());
    }

    // 乘法测试
    @Test
    void testMultiply_TwoNumbers() {
        List<Double> numbers = Arrays.asList(4.0, 5.0);
        CalculationResponse response = calculatorService.multiply(numbers);
        
        assertEquals(20.0, response.getResult(), 0.001);
        assertEquals("multiply", response.getOperation());
        assertEquals(numbers, response.getOperands());
    }

    @Test
    void testMultiply_MultipleNumbers() {
        List<Double> numbers = Arrays.asList(2.0, 3.0, 4.0);
        CalculationResponse response = calculatorService.multiply(numbers);
        
        assertEquals(24.0, response.getResult(), 0.001);
        assertEquals("multiply", response.getOperation());
    }

    @Test
    void testMultiply_WithZero() {
        List<Double> numbers = Arrays.asList(5.0, 0.0, 3.0);
        CalculationResponse response = calculatorService.multiply(numbers);
        
        assertEquals(0.0, response.getResult(), 0.001);
        assertEquals("multiply", response.getOperation());
    }

    @Test
    void testMultiply_WithNegativeNumbers() {
        List<Double> numbers = Arrays.asList(-2.0, 3.0, -4.0);
        CalculationResponse response = calculatorService.multiply(numbers);
        
        assertEquals(24.0, response.getResult(), 0.001); // -2 * 3 * -4 = 24
        assertEquals("multiply", response.getOperation());
    }

    @Test
    void testMultiply_WithDecimals() {
        List<Double> numbers = Arrays.asList(2.5, 4.0);
        CalculationResponse response = calculatorService.multiply(numbers);
        
        assertEquals(10.0, response.getResult(), 0.001);
        assertEquals("multiply", response.getOperation());
    }

    // 除法测试
    @Test
    void testDivide_TwoNumbers() {
        List<Double> numbers = Arrays.asList(20.0, 4.0);
        CalculationResponse response = calculatorService.divide(numbers);
        
        assertEquals(5.0, response.getResult(), 0.001);
        assertEquals("divide", response.getOperation());
        assertEquals(numbers, response.getOperands());
    }

    @Test
    void testDivide_MultipleNumbers() {
        List<Double> numbers = Arrays.asList(24.0, 2.0, 3.0);
        CalculationResponse response = calculatorService.divide(numbers);
        
        assertEquals(4.0, response.getResult(), 0.001); // 24 / 2 / 3 = 4
        assertEquals("divide", response.getOperation());
    }

    @Test
    void testDivide_ResultingInDecimal() {
        List<Double> numbers = Arrays.asList(7.0, 2.0);
        CalculationResponse response = calculatorService.divide(numbers);
        
        assertEquals(3.5, response.getResult(), 0.001);
        assertEquals("divide", response.getOperation());
    }

    @Test
    void testDivide_WithNegativeNumbers() {
        List<Double> numbers = Arrays.asList(-20.0, 4.0);
        CalculationResponse response = calculatorService.divide(numbers);
        
        assertEquals(-5.0, response.getResult(), 0.001);
        assertEquals("divide", response.getOperation());
    }

    @Test
    void testDivide_ByZero_ThrowsException() {
        List<Double> numbers = Arrays.asList(10.0, 0.0);
        
        DivisionByZeroException exception = assertThrows(
            DivisionByZeroException.class,
            () -> calculatorService.divide(numbers)
        );
        
        assertEquals("Division by zero is not allowed", exception.getMessage());
        assertEquals("DIVISION_BY_ZERO", exception.getErrorCode());
    }

    @Test
    void testDivide_ZeroByNumber() {
        List<Double> numbers = Arrays.asList(0.0, 5.0);
        CalculationResponse response = calculatorService.divide(numbers);
        
        assertEquals(0.0, response.getResult(), 0.001);
        assertEquals("divide", response.getOperation());
    }

    // 输入验证测试
    @Test
    void testAdd_InvalidInput_ThrowsException() {
        List<Double> invalidNumbers = Collections.singletonList(5.0);
        
        InvalidInputException exception = assertThrows(
            InvalidInputException.class,
            () -> calculatorService.add(invalidNumbers)
        );
        
        assertTrue(exception.getMessage().contains("At least 2 numbers are required"));
        assertEquals("INVALID_INPUT", exception.getErrorCode());
    }

    @Test
    void testSubtract_NullInput_ThrowsException() {
        InvalidInputException exception = assertThrows(
            InvalidInputException.class,
            () -> calculatorService.subtract(null)
        );
        
        assertEquals("Numbers list cannot be null", exception.getMessage());
    }

    @Test
    void testMultiply_EmptyList_ThrowsException() {
        List<Double> emptyList = Collections.emptyList();
        
        InvalidInputException exception = assertThrows(
            InvalidInputException.class,
            () -> calculatorService.multiply(emptyList)
        );
        
        assertEquals("Numbers list cannot be empty", exception.getMessage());
    }

    @Test
    void testDivide_WithNaN_ThrowsException() {
        List<Double> numbersWithNaN = Arrays.asList(10.0, Double.NaN);
        
        InvalidInputException exception = assertThrows(
            InvalidInputException.class,
            () -> calculatorService.divide(numbersWithNaN)
        );
        
        assertTrue(exception.getMessage().contains("is NaN"));
    }
}