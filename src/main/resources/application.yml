server:
  port: 8080
  netty:
    connection-timeout: 30s
    idle-timeout: 60s

spring:
  application:
    name: Calculator MCP Server
  webflux:
    base-path: /
  jackson:
    serialization:
      fail-on-empty-beans: false
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false

# MCP服务器配置
mcp:
  server:
    name: "Calculator MCP Server"
    version: "1.0.0"
    description: "A simple calculator service providing basic arithmetic operations via SSE"
    max-concurrent-requests: 100
    request-timeout: 30s
    transport: "sse"
    sse:
      endpoint: "/mcp/sse"
      message-endpoint: "/mcp/message"
      heartbeat-interval: 30s
  tools:
    calculator_add:
      enabled: true
      description: "Execute addition operation on two or more numbers"
    calculator_subtract:
      enabled: true
      description: "Execute subtraction operation on numbers in sequence"
    calculator_multiply:
      enabled: true
      description: "Execute multiplication operation on two or more numbers"
    calculator_divide:
      enabled: true
      description: "Execute division operation on numbers in sequence"

# 日志配置
logging:
  level:
    root: INFO
    io.kt666.mcp: DEBUG
    io.modelcontextprotocol: INFO
    org.springframework.web: INFO
    reactor.netty: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/calculator-mcp-server.log
    max-size: 10MB
    max-history: 30

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
      base-path: /actuator
  endpoint:
    health:
      show-details: when-authorized
      show-components: always
  health:
    defaults:
      enabled: true
  info:
    env:
      enabled: true
    java:
      enabled: true
    os:
      enabled: true

# 应用信息
info:
  app:
    name: Calculator MCP Server
    description: A simple calculator service providing basic arithmetic operations via MCP protocol
    version: 1.0.0
    encoding: UTF-8
    java:
      version: 21

# 性能调优配置
reactor:
  netty:
    pool:
      max-connections: 100
      max-idle-time: 30s
      max-life-time: 60s

# 开发环境配置
---
spring:
  config:
    activate:
      on-profile: dev
  devtools:
    restart:
      enabled: false
    livereload:
      enabled: false
  
logging:
  level:
    io.kt666.mcp: TRACE
    org.springframework.web: DEBUG
    reactor.netty: DEBUG

server:
  port: 8080

# 生产环境配置
---
spring:
  config:
    activate:
      on-profile: prod

logging:
  level:
    root: WARN
    io.kt666.mcp: INFO
  file:
    name: /var/log/calculator-mcp-server/application.log

server:
  port: 8080
  netty:
    connection-timeout: 20s
    idle-timeout: 45s

mcp:
  server:
    max-concurrent-requests: 200

# 测试环境配置
---
spring:
  config:
    activate:
      on-profile: test

logging:
  level:
    root: WARN
    io.kt666.mcp: DEBUG

server:
  port: 0  # 随机端口用于测试