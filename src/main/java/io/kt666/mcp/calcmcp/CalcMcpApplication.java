package io.kt666.mcp.calcmcp;

import io.kt666.mcp.calcmcp.mcp.McpServer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;

/**
 * Calculator MCP Server 主应用类
 * 
 * 这是一个基于MCP协议的计算器服务，提供基本的数学运算功能。
 * 支持stdio和SSE两种传输方式与MCP客户端集成，支持加减乘除等基本计算操作。
 */
@SpringBootApplication
public class CalcMcpApplication {

    private static final Logger logger = LoggerFactory.getLogger(CalcMcpApplication.class);

    public static void main(String[] args) {
        try {
            // 检查传输方式
            String transport = getTransportMode(args);
            
            SpringApplication app = new SpringApplication(CalcMcpApplication.class);
            
            if ("sse".equalsIgnoreCase(transport)) {
                // SSE模式 - 启动Web服务器
                System.setProperty("spring.main.web-application-type", "reactive");
                app.setWebApplicationType(org.springframework.boot.WebApplicationType.REACTIVE);
                
                ConfigurableApplicationContext context = app.run(args);
                logger.info("Calculator MCP Server started with SSE transport on port 8080");
                logger.info("SSE endpoint: http://localhost:8080/mcp/sse");
                logger.info("Message endpoint: http://localhost:8080/mcp/message");
                logger.info("Health check: http://localhost:8080/mcp/health");
                
                // 保持应用运行
                Runtime.getRuntime().addShutdownHook(new Thread(() -> {
                    logger.info("Shutting down Calculator MCP Server...");
                    context.close();
                }));
                
            } else {
                // stdio模式
                System.setProperty("spring.main.web-application-type", "none");
                app.setWebApplicationType(org.springframework.boot.WebApplicationType.NONE);
                
                ConfigurableApplicationContext context = app.run(args);
                
                // 启动stdio MCP服务器
                McpServer mcpServer = context.getBean(McpServer.class);
                mcpServer.start();
                
                logger.info("Calculator MCP Server started with stdio transport");
            }
        } catch (Exception e) {
            logger.error("Failed to start Calculator MCP Server", e);
            System.exit(1);
        }
    }
    
    /**
     * 从命令行参数中获取传输模式
     * 
     * @param args 命令行参数
     * @return 传输模式 ("stdio" 或 "sse")
     */
    private static String getTransportMode(String[] args) {
        // 检查命令行参数
        for (String arg : args) {
            if (arg.startsWith("--transport=")) {
                return arg.substring("--transport=".length());
            }
            if ("--sse".equals(arg)) {
                return "sse";
            }
            if ("--stdio".equals(arg)) {
                return "stdio";
            }
        }
        
        // 检查环境变量
        String envTransport = System.getenv("MCP_TRANSPORT");
        if (envTransport != null && !envTransport.isEmpty()) {
            return envTransport;
        }
        
        // 检查系统属性
        String propTransport = System.getProperty("mcp.transport");
        if (propTransport != null && !propTransport.isEmpty()) {
            return propTransport;
        }
        
        // 默认使用stdio模式
        return "stdio";
    }
}
