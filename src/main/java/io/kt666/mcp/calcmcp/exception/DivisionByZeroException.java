package io.kt666.mcp.calcmcp.exception;

/**
 * 除零异常
 */
public class DivisionByZeroException extends CalculationException {
    
    public DivisionByZeroException(String operation) {
        super("DIVISION_BY_ZERO", "Division by zero is not allowed", operation);
    }

    public DivisionByZeroException(String operation, Throwable cause) {
        super("DIVISION_BY_ZERO", "Division by zero is not allowed", operation, cause);
    }
}