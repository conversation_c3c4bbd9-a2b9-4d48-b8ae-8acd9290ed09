package io.kt666.mcp.calcmcp.exception;

/**
 * 计算异常基类
 */
public class CalculationException extends RuntimeException {
    private final String errorCode;
    private final String operation;

    public CalculationException(String errorCode, String message, String operation) {
        super(message);
        this.errorCode = errorCode;
        this.operation = operation;
    }

    public CalculationException(String errorCode, String message, String operation, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
        this.operation = operation;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public String getOperation() {
        return operation;
    }
}