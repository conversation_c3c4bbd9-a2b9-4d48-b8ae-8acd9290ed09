package io.kt666.mcp.calcmcp.config;

import io.kt666.mcp.calcmcp.service.CalculatorService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * MCP服务器配置类
 * 配置MCP服务器和工具注册
 */
@Configuration
public class McpConfiguration {

    private final CalculatorService calculatorService;

    @Autowired
    public McpConfiguration(CalculatorService calculatorService) {
        this.calculatorService = calculatorService;
    }

    /**
     * 配置MCP服务器信息
     */
    @Bean
    public Map<String, Object> mcpServerInfo() {
        Map<String, Object> serverInfo = new HashMap<>();
        serverInfo.put("name", "Calculator MCP Server");
        serverInfo.put("version", "1.0.0");
        serverInfo.put("description", "A simple calculator service providing basic arithmetic operations");
        serverInfo.put("protocol", "MCP");
        serverInfo.put("capabilities", Arrays.asList("tools", "resources"));
        return serverInfo;
    }

    /**
     * 注册计算器工具定义
     */
    @Bean
    public Map<String, Object> toolDefinitions() {
        Map<String, Object> tools = new HashMap<>();
        
        // 注册加法工具
        tools.put("calculator_add", createAddToolDefinition());
        
        // 注册减法工具
        tools.put("calculator_subtract", createSubtractToolDefinition());
        
        // 注册乘法工具
        tools.put("calculator_multiply", createMultiplyToolDefinition());
        
        // 注册除法工具
        tools.put("calculator_divide", createDivideToolDefinition());
        
        return tools;
    }

    /**
     * 创建加法工具定义
     */
    private Map<String, Object> createAddToolDefinition() {
        Map<String, Object> tool = new HashMap<>();
        tool.put("name", "calculator_add");
        tool.put("description", "Execute addition operation on two or more numbers");
        tool.put("inputSchema", createNumbersInputSchema());
        return tool;
    }

    /**
     * 创建减法工具定义
     */
    private Map<String, Object> createSubtractToolDefinition() {
        Map<String, Object> tool = new HashMap<>();
        tool.put("name", "calculator_subtract");
        tool.put("description", "Execute subtraction operation on numbers in sequence");
        tool.put("inputSchema", createNumbersInputSchema());
        return tool;
    }

    /**
     * 创建乘法工具定义
     */
    private Map<String, Object> createMultiplyToolDefinition() {
        Map<String, Object> tool = new HashMap<>();
        tool.put("name", "calculator_multiply");
        tool.put("description", "Execute multiplication operation on two or more numbers");
        tool.put("inputSchema", createNumbersInputSchema());
        return tool;
    }

    /**
     * 创建除法工具定义
     */
    private Map<String, Object> createDivideToolDefinition() {
        Map<String, Object> tool = new HashMap<>();
        tool.put("name", "calculator_divide");
        tool.put("description", "Execute division operation on numbers in sequence");
        tool.put("inputSchema", createNumbersInputSchema());
        return tool;
    }

    /**
     * 创建数字输入参数的JSON Schema
     */
    private Map<String, Object> createNumbersInputSchema() {
        Map<String, Object> schema = new HashMap<>();
        schema.put("type", "object");
        
        Map<String, Object> properties = new HashMap<>();
        Map<String, Object> numbersProperty = new HashMap<>();
        numbersProperty.put("type", "array");
        numbersProperty.put("items", Map.of("type", "number"));
        numbersProperty.put("minItems", 2);
        numbersProperty.put("description", "Array of numbers for calculation (at least 2 numbers required)");
        properties.put("numbers", numbersProperty);
        
        schema.put("properties", properties);
        schema.put("required", Arrays.asList("numbers"));
        
        return schema;
    }

    /**
     * 处理工具调用的方法映射
     */
    @Bean
    public Map<String, java.util.function.Function<Map<String, Object>, Map<String, Object>>> toolHandlers(
            CalculatorService calculatorService) {
        Map<String, java.util.function.Function<Map<String, Object>, Map<String, Object>>> handlers = new HashMap<>();
        
        handlers.put("calculator_add", arguments -> handleAddOperation(arguments, calculatorService));
        handlers.put("calculator_subtract", arguments -> handleSubtractOperation(arguments, calculatorService));
        handlers.put("calculator_multiply", arguments -> handleMultiplyOperation(arguments, calculatorService));
        handlers.put("calculator_divide", arguments -> handleDivideOperation(arguments, calculatorService));
        
        return handlers;
    }

    /**
     * 处理加法操作
     */
    private static Map<String, Object> handleAddOperation(Map<String, Object> arguments, CalculatorService calculatorService) {
        List<Double> numbers = extractNumbers(arguments);
        var response = calculatorService.add(numbers);
        
        return createSuccessResponse(response.getResult(), response.getOperation(), 
                response.getOperands(), response.getDescription());
    }

    /**
     * 处理减法操作
     */
    private static Map<String, Object> handleSubtractOperation(Map<String, Object> arguments, CalculatorService calculatorService) {
        List<Double> numbers = extractNumbers(arguments);
        var response = calculatorService.subtract(numbers);
        
        return createSuccessResponse(response.getResult(), response.getOperation(), 
                response.getOperands(), response.getDescription());
    }

    /**
     * 处理乘法操作
     */
    private static Map<String, Object> handleMultiplyOperation(Map<String, Object> arguments, CalculatorService calculatorService) {
        List<Double> numbers = extractNumbers(arguments);
        var response = calculatorService.multiply(numbers);
        
        return createSuccessResponse(response.getResult(), response.getOperation(), 
                response.getOperands(), response.getDescription());
    }

    /**
     * 处理除法操作
     */
    private static Map<String, Object> handleDivideOperation(Map<String, Object> arguments, CalculatorService calculatorService) {
        List<Double> numbers = extractNumbers(arguments);
        var response = calculatorService.divide(numbers);
        
        return createSuccessResponse(response.getResult(), response.getOperation(), 
                response.getOperands(), response.getDescription());
    }

    /**
     * 从参数中提取数字列表
     */
    @SuppressWarnings("unchecked")
    private static List<Double> extractNumbers(Map<String, Object> arguments) {
        List<Object> numberObjects = (List<Object>) arguments.get("numbers");
        
        return numberObjects.stream()
                .map(obj -> {
                    if (obj instanceof Number) {
                        return ((Number) obj).doubleValue();
                    }
                    throw new IllegalArgumentException("Invalid number format: " + obj);
                })
                .toList();
    }

    /**
     * 创建成功响应
     */
    private static Map<String, Object> createSuccessResponse(Double result, String operation, 
                                                     List<Double> operands, String description) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("result", result);
        response.put("operation", operation);
        response.put("operands", operands);
        response.put("description", description);
        response.put("timestamp", System.currentTimeMillis());
        return response;
    }

    /**
     * 创建错误响应
     */
    private static Map<String, Object> createErrorResponse(String errorMessage) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("error", errorMessage);
        response.put("timestamp", System.currentTimeMillis());
        return response;
    }
}