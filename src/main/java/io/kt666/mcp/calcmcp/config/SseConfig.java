package io.kt666.mcp.calcmcp.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * SSE配置类
 */
@Configuration
@ConfigurationProperties(prefix = "mcp.server.sse")
public class SseConfig {
    
    private String endpoint = "/mcp/sse";
    private String messageEndpoint = "/mcp/message";
    private long heartbeatInterval = 30000; // 30秒
    private int maxConnections = 100;
    private long connectionTimeout = 60000; // 60秒
    
    public String getEndpoint() {
        return endpoint;
    }
    
    public void setEndpoint(String endpoint) {
        this.endpoint = endpoint;
    }
    
    public String getMessageEndpoint() {
        return messageEndpoint;
    }
    
    public void setMessageEndpoint(String messageEndpoint) {
        this.messageEndpoint = messageEndpoint;
    }
    
    public long getHeartbeatInterval() {
        return heartbeatInterval;
    }
    
    public void setHeartbeatInterval(long heartbeatInterval) {
        this.heartbeatInterval = heartbeatInterval;
    }
    
    public int getMaxConnections() {
        return maxConnections;
    }
    
    public void setMaxConnections(int maxConnections) {
        this.maxConnections = maxConnections;
    }
    
    public long getConnectionTimeout() {
        return connectionTimeout;
    }
    
    public void setConnectionTimeout(long connectionTimeout) {
        this.connectionTimeout = connectionTimeout;
    }
}