package io.kt666.mcp.calcmcp.mcp;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.concurrent.atomic.AtomicLong;

/**
 * MCP Protocol Server Implementation
 * Handles stdio communication with Kiro IDE
 */
@Component
public class McpServer {
    
    private static final Logger logger = LoggerFactory.getLogger(McpServer.class);
    private final ObjectMapper objectMapper = new ObjectMapper();
    private final AtomicLong requestId = new AtomicLong(0);
    
    public void start() {
        logger.info("Starting MCP Server with stdio communication...");
        
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(System.in))) {
            String line;
            while ((line = reader.readLine()) != null) {
                try {
                    JsonNode request = objectMapper.readTree(line);
                    handleRequest(request);
                } catch (Exception e) {
                    logger.error("Error processing request: {}", line, e);
                    sendError(-1, "Invalid JSON request", e.getMessage());
                }
            }
        } catch (IOException e) {
            logger.error("Error reading from stdin", e);
        }
    }
    
    private void handleRequest(JsonNode request) {
        String method = request.path("method").asText();
        JsonNode id = request.path("id");
        JsonNode params = request.path("params");
        
        logger.info("Received request: method={}, id={}", method, id);
        
        switch (method) {
            case "initialize":
                handleInitialize(id, params);
                break;
            case "tools/list":
                handleToolsList(id);
                break;
            case "tools/call":
                handleToolCall(id, params);
                break;
            default:
                sendError(id.asLong(-1), "Method not found", "Unknown method: " + method);
        }
    }
    
    private void handleInitialize(JsonNode id, JsonNode params) {
        ObjectNode response = objectMapper.createObjectNode();
        response.put("jsonrpc", "2.0");
        response.set("id", id);
        
        ObjectNode result = objectMapper.createObjectNode();
        result.put("protocolVersion", "2024-11-05");
        
        ObjectNode capabilities = objectMapper.createObjectNode();
        ObjectNode tools = objectMapper.createObjectNode();
        tools.put("listChanged", false);
        capabilities.set("tools", tools);
        result.set("capabilities", capabilities);
        
        ObjectNode serverInfo = objectMapper.createObjectNode();
        serverInfo.put("name", "Calculator MCP Server");
        serverInfo.put("version", "1.0.0");
        result.set("serverInfo", serverInfo);
        
        response.set("result", result);
        sendResponse(response);
    }
    
    private void handleToolsList(JsonNode id) {
        ObjectNode response = objectMapper.createObjectNode();
        response.put("jsonrpc", "2.0");
        response.set("id", id);
        
        ObjectNode result = objectMapper.createObjectNode();
        ArrayNode tools = objectMapper.createArrayNode();
        
        // Add calculator tools
        tools.add(createTool("calculator_add", "Add two numbers", 
            new String[]{"a", "b"}, new String[]{"First number", "Second number"}));
        tools.add(createTool("calculator_subtract", "Subtract two numbers", 
            new String[]{"a", "b"}, new String[]{"First number", "Second number"}));
        tools.add(createTool("calculator_multiply", "Multiply two numbers", 
            new String[]{"a", "b"}, new String[]{"First number", "Second number"}));
        tools.add(createTool("calculator_divide", "Divide two numbers", 
            new String[]{"a", "b"}, new String[]{"Dividend", "Divisor"}));
        
        result.set("tools", tools);
        response.set("result", result);
        sendResponse(response);
    }
    
    private ObjectNode createTool(String name, String description, String[] paramNames, String[] paramDescriptions) {
        ObjectNode tool = objectMapper.createObjectNode();
        tool.put("name", name);
        tool.put("description", description);
        
        ObjectNode inputSchema = objectMapper.createObjectNode();
        inputSchema.put("type", "object");
        
        ObjectNode properties = objectMapper.createObjectNode();
        ArrayNode required = objectMapper.createArrayNode();
        
        for (int i = 0; i < paramNames.length; i++) {
            ObjectNode param = objectMapper.createObjectNode();
            param.put("type", "number");
            param.put("description", paramDescriptions[i]);
            properties.set(paramNames[i], param);
            required.add(paramNames[i]);
        }
        
        inputSchema.set("properties", properties);
        inputSchema.set("required", required);
        tool.set("inputSchema", inputSchema);
        
        return tool;
    }
    
    private void handleToolCall(JsonNode id, JsonNode params) {
        String toolName = params.path("name").asText();
        JsonNode arguments = params.path("arguments");
        
        try {
            String result = executeCalculation(toolName, arguments);
            sendToolResult(id, result);
        } catch (Exception e) {
            sendError(id.asLong(-1), "Tool execution failed", e.getMessage());
        }
    }
    
    private String executeCalculation(String toolName, JsonNode arguments) throws Exception {
        double a = arguments.path("a").asDouble();
        double b = arguments.path("b").asDouble();
        
        BigDecimal bdA = BigDecimal.valueOf(a);
        BigDecimal bdB = BigDecimal.valueOf(b);
        BigDecimal result;
        
        switch (toolName) {
            case "calculator_add":
                result = bdA.add(bdB);
                return String.format("%.2f + %.2f = %.2f", a, b, result.doubleValue());
                
            case "calculator_subtract":
                result = bdA.subtract(bdB);
                return String.format("%.2f - %.2f = %.2f", a, b, result.doubleValue());
                
            case "calculator_multiply":
                result = bdA.multiply(bdB);
                return String.format("%.2f × %.2f = %.2f", a, b, result.doubleValue());
                
            case "calculator_divide":
                if (bdB.compareTo(BigDecimal.ZERO) == 0) {
                    throw new ArithmeticException("Division by zero is not allowed");
                }
                result = bdA.divide(bdB, 10, RoundingMode.HALF_UP);
                return String.format("%.2f ÷ %.2f = %.2f", a, b, result.doubleValue());
                
            default:
                throw new IllegalArgumentException("Unknown tool: " + toolName);
        }
    }
    
    private void sendToolResult(JsonNode id, String result) {
        ObjectNode response = objectMapper.createObjectNode();
        response.put("jsonrpc", "2.0");
        response.set("id", id);
        
        ObjectNode resultObj = objectMapper.createObjectNode();
        ArrayNode content = objectMapper.createArrayNode();
        
        ObjectNode textContent = objectMapper.createObjectNode();
        textContent.put("type", "text");
        textContent.put("text", result);
        content.add(textContent);
        
        resultObj.set("content", content);
        response.set("result", resultObj);
        
        sendResponse(response);
    }
    
    private void sendError(long id, String message, String details) {
        ObjectNode response = objectMapper.createObjectNode();
        response.put("jsonrpc", "2.0");
        response.put("id", id);
        
        ObjectNode error = objectMapper.createObjectNode();
        error.put("code", -32603);
        error.put("message", message);
        if (details != null) {
            error.put("data", details);
        }
        
        response.set("error", error);
        sendResponse(response);
    }
    
    private void sendResponse(ObjectNode response) {
        try {
            String json = objectMapper.writeValueAsString(response);
            System.out.println(json);
            System.out.flush();
            logger.info("Sent response: {}", json);
        } catch (Exception e) {
            logger.error("Error sending response", e);
        }
    }
}