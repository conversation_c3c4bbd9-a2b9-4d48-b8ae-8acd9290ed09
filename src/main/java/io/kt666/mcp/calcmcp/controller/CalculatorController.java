package io.kt666.mcp.calcmcp.controller;

import io.kt666.mcp.calcmcp.service.CalculatorService;
import io.kt666.mcp.calcmcp.service.InputValidator;
import io.kt666.mcp.calcmcp.util.ResponseFormatter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.util.*;

/**
 * MCP计算器控制器
 * 处理MCP工具调用请求和工具列表查询
 */
@RestController
@RequestMapping("/mcp")
public class CalculatorController {

    private final CalculatorService calculatorService;
    private final InputValidator inputValidator;
    private final ResponseFormatter responseFormatter;

    @Autowired
    public CalculatorController(
            CalculatorService calculatorService,
            InputValidator inputValidator,
            ResponseFormatter responseFormatter) {
        this.calculatorService = calculatorService;
        this.inputValidator = inputValidator;
        this.responseFormatter = responseFormatter;
    }

    /**
     * 处理MCP工具调用请求
     * 
     * @param toolName 工具名称
     * @param arguments 工具参数
     * @return 工具执行结果
     */
    @PostMapping("/tools/{toolName}/call")
    public Mono<ResponseEntity<Map<String, Object>>> handleToolCall(
            @PathVariable String toolName,
            @RequestBody Map<String, Object> arguments) {
        
        return Mono.fromCallable(() -> {
            try {
                // 验证工具是否存在
                if (!isValidTool(toolName)) {
                    return ResponseEntity.badRequest()
                            .body(createErrorResponse("Tool not found: " + toolName));
                }

                // 执行输入验证
                validateToolArguments(arguments);

                // 调用相应的工具处理器
                Map<String, Object> result = handleToolOperation(toolName, arguments);

                return ResponseEntity.ok(result);

            } catch (io.kt666.mcp.calcmcp.exception.CalculationException e) {
                return ResponseEntity.badRequest()
                        .body(responseFormatter.formatErrorResponse(e.getErrorCode(), e.getMessage(), e.getOperation()));
            } catch (Exception e) {
                return ResponseEntity.badRequest()
                        .body(createErrorResponse(e.getMessage()));
            }
        });
    }

    /**
     * 返回可用工具列表
     * 
     * @return 工具定义列表
     */
    @GetMapping("/tools")
    public Mono<ResponseEntity<Map<String, Object>>> listTools() {
        return Mono.fromCallable(() -> {
            Map<String, Object> toolDefinitions = createToolDefinitions();
            Map<String, Object> serverInfo = createServerInfo();
            Map<String, Object> response = responseFormatter.formatToolListResponse(toolDefinitions, serverInfo);
            return ResponseEntity.ok(response);
        });
    }

    /**
     * 获取特定工具的定义
     * 
     * @param toolName 工具名称
     * @return 工具定义
     */
    @GetMapping("/tools/{toolName}")
    public Mono<ResponseEntity<Map<String, Object>>> getToolDefinition(@PathVariable String toolName) {
        return Mono.fromCallable(() -> {
            Map<String, Object> toolDefinitions = createToolDefinitions();
            if (!toolDefinitions.containsKey(toolName)) {
                return ResponseEntity.notFound().build();
            }

            @SuppressWarnings("unchecked")
            Map<String, Object> toolDef = (Map<String, Object>) toolDefinitions.get(toolName);
            Map<String, Object> response = responseFormatter.formatToolDefinitionResponse(toolName, toolDef);
            return ResponseEntity.ok(response);
        });
    }

    /**
     * 获取服务器信息
     * 
     * @return MCP服务器信息
     */
    @GetMapping("/server/info")
    public Mono<ResponseEntity<Map<String, Object>>> getServerInfo() {
        return Mono.fromCallable(() -> {
            Map<String, Object> serverInfo = createServerInfo();
            Map<String, Object> response = responseFormatter.formatServerInfoResponse(serverInfo);
            return ResponseEntity.ok(response);
        });
    }

    /**
     * 健康检查端点
     * 
     * @return 服务状态
     */
    @GetMapping("/health")
    public Mono<ResponseEntity<Map<String, Object>>> healthCheck() {
        return Mono.fromCallable(() -> {
            // 只返回我们定义的计算器工具
            java.util.Set<String> calculatorTools = java.util.Set.of(
                "calculator_add", "calculator_subtract", "calculator_multiply", "calculator_divide"
            );
            Map<String, Object> response = responseFormatter.formatHealthCheckResponse(calculatorTools);
            return ResponseEntity.ok(response);
        });
    }

    /**
     * 验证工具参数
     * 
     * @param arguments 工具参数
     * @throws IllegalArgumentException 当参数无效时
     */
    private void validateToolArguments(Map<String, Object> arguments) {
        if (arguments == null) {
            throw new IllegalArgumentException("Arguments cannot be null");
        }

        if (!arguments.containsKey("numbers")) {
            throw new IllegalArgumentException("Missing required parameter: numbers");
        }

        // 使用InputValidator进行进一步验证
        Object numbersObj = arguments.get("numbers");
        if (!(numbersObj instanceof java.util.List)) {
            throw new IllegalArgumentException("Parameter 'numbers' must be a list");
        }

        @SuppressWarnings("unchecked")
        java.util.List<Object> numbersList = (java.util.List<Object>) numbersObj;
        
        // 转换为Double列表并验证
        java.util.List<Double> numbers = numbersList.stream()
                .map(obj -> {
                    if (obj instanceof Number) {
                        return ((Number) obj).doubleValue();
                    }
                    throw new IllegalArgumentException("All elements in 'numbers' must be numeric");
                })
                .toList();

        inputValidator.validateNumbers(numbers);
    }

    /**
     * 验证工具是否有效
     */
    private boolean isValidTool(String toolName) {
        return Arrays.asList("calculator_add", "calculator_subtract", "calculator_multiply", "calculator_divide")
                .contains(toolName);
    }

    /**
     * 处理工具操作
     */
    private Map<String, Object> handleToolOperation(String toolName, Map<String, Object> arguments) {
        List<Double> numbers = extractNumbers(arguments);
        
        switch (toolName) {
            case "calculator_add":
                var addResponse = calculatorService.add(numbers);
                return responseFormatter.formatSuccessResponse(addResponse);
            case "calculator_subtract":
                var subtractResponse = calculatorService.subtract(numbers);
                return responseFormatter.formatSuccessResponse(subtractResponse);
            case "calculator_multiply":
                var multiplyResponse = calculatorService.multiply(numbers);
                return responseFormatter.formatSuccessResponse(multiplyResponse);
            case "calculator_divide":
                var divideResponse = calculatorService.divide(numbers);
                return responseFormatter.formatSuccessResponse(divideResponse);
            default:
                throw new IllegalArgumentException("Unknown tool: " + toolName);
        }
    }

    /**
     * 从参数中提取数字列表
     */
    @SuppressWarnings("unchecked")
    private List<Double> extractNumbers(Map<String, Object> arguments) {
        List<Object> numberObjects = (List<Object>) arguments.get("numbers");
        
        return numberObjects.stream()
                .map(obj -> {
                    if (obj instanceof Number) {
                        return ((Number) obj).doubleValue();
                    }
                    throw new IllegalArgumentException("Invalid number format: " + obj);
                })
                .toList();
    }

    /**
     * 创建工具定义
     */
    private Map<String, Object> createToolDefinitions() {
        Map<String, Object> tools = new HashMap<>();
        
        tools.put("calculator_add", createAddToolDefinition());
        tools.put("calculator_subtract", createSubtractToolDefinition());
        tools.put("calculator_multiply", createMultiplyToolDefinition());
        tools.put("calculator_divide", createDivideToolDefinition());
        
        return tools;
    }

    /**
     * 创建服务器信息
     */
    private Map<String, Object> createServerInfo() {
        Map<String, Object> serverInfo = new HashMap<>();
        serverInfo.put("name", "Calculator MCP Server");
        serverInfo.put("version", "1.0.0");
        serverInfo.put("description", "A simple calculator service providing basic arithmetic operations");
        serverInfo.put("protocol", "MCP");
        serverInfo.put("capabilities", Arrays.asList("tools", "resources"));
        return serverInfo;
    }

    /**
     * 创建加法工具定义
     */
    private Map<String, Object> createAddToolDefinition() {
        Map<String, Object> tool = new HashMap<>();
        tool.put("name", "calculator_add");
        tool.put("description", "Execute addition operation on two or more numbers");
        tool.put("inputSchema", createNumbersInputSchema());
        return tool;
    }

    /**
     * 创建减法工具定义
     */
    private Map<String, Object> createSubtractToolDefinition() {
        Map<String, Object> tool = new HashMap<>();
        tool.put("name", "calculator_subtract");
        tool.put("description", "Execute subtraction operation on numbers in sequence");
        tool.put("inputSchema", createNumbersInputSchema());
        return tool;
    }

    /**
     * 创建乘法工具定义
     */
    private Map<String, Object> createMultiplyToolDefinition() {
        Map<String, Object> tool = new HashMap<>();
        tool.put("name", "calculator_multiply");
        tool.put("description", "Execute multiplication operation on two or more numbers");
        tool.put("inputSchema", createNumbersInputSchema());
        return tool;
    }

    /**
     * 创建除法工具定义
     */
    private Map<String, Object> createDivideToolDefinition() {
        Map<String, Object> tool = new HashMap<>();
        tool.put("name", "calculator_divide");
        tool.put("description", "Execute division operation on numbers in sequence");
        tool.put("inputSchema", createNumbersInputSchema());
        return tool;
    }

    /**
     * 创建数字输入参数的JSON Schema
     */
    private Map<String, Object> createNumbersInputSchema() {
        Map<String, Object> schema = new HashMap<>();
        schema.put("type", "object");
        
        Map<String, Object> properties = new HashMap<>();
        Map<String, Object> numbersProperty = new HashMap<>();
        numbersProperty.put("type", "array");
        numbersProperty.put("items", Map.of("type", "number"));
        numbersProperty.put("minItems", 2);
        numbersProperty.put("description", "Array of numbers for calculation (at least 2 numbers required)");
        properties.put("numbers", numbersProperty);
        
        schema.put("properties", properties);
        schema.put("required", Arrays.asList("numbers"));
        
        return schema;
    }

    /**
     * 创建错误响应
     * 
     * @param errorMessage 错误消息
     * @return 错误响应Map
     */
    private Map<String, Object> createErrorResponse(String errorMessage) {
        return responseFormatter.formatErrorResponse("REQUEST_ERROR", errorMessage, "tool_call");
    }
}