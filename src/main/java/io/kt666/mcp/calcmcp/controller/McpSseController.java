package io.kt666.mcp.calcmcp.controller;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.kt666.mcp.calcmcp.service.CalculatorService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicLong;

/**
 * MCP SSE Controller
 * 处理基于SSE的MCP协议通信
 */
@RestController
@RequestMapping("/mcp")
@CrossOrigin(origins = "*")
public class McpSseController {

    private static final Logger logger = LoggerFactory.getLogger(McpSseController.class);
    private final ObjectMapper objectMapper = new ObjectMapper();
    private final AtomicLong requestId = new AtomicLong(0);
    private final CalculatorService calculatorService;

    @Autowired
    public McpSseController(CalculatorService calculatorService) {
        this.calculatorService = calculatorService;
    }

    /**
     * SSE端点 - 处理MCP协议消息
     */
    @GetMapping(value = "/sse", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<ServerSentEvent<String>> sseEndpoint() {
        logger.info("SSE connection established");
        
        return Flux.interval(Duration.ofSeconds(30))
                .startWith(0L) // 立即发送第一个事件
                .map(sequence -> {
                    if (sequence == 0) {
                        return ServerSentEvent.<String>builder()
                                .event("message")
                                .data("{\"type\":\"connection\",\"status\":\"connected\"}")
                                .build();
                    } else {
                        return ServerSentEvent.<String>builder()
                                .id(String.valueOf(sequence))
                                .event("ping")
                                .data("ping")
                                .build();
                    }
                })
                .doOnCancel(() -> logger.info("SSE connection cancelled"))
                .doOnError(error -> logger.error("SSE connection error", error));
    }

    /**
     * 处理MCP请求
     */
    @PostMapping(value = "/message", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public Mono<String> handleMcpMessage(@RequestBody String requestBody) {
        return Mono.fromCallable(() -> {
            try {
                JsonNode request = objectMapper.readTree(requestBody);
                return handleRequest(request);
            } catch (Exception e) {
                logger.error("Error processing MCP request: {}", requestBody, e);
                return createErrorResponse(-1, "Invalid JSON request", e.getMessage());
            }
        });
    }

    /**
     * 获取服务器信息
     */
    @GetMapping(value = "/info", produces = MediaType.APPLICATION_JSON_VALUE)
    public Mono<String> getServerInfo() {
        return Mono.fromCallable(() -> {
            ObjectNode info = objectMapper.createObjectNode();
            info.put("name", "Calculator MCP Server");
            info.put("version", "1.0.0");
            info.put("protocol", "SSE");
            info.put("description", "A calculator service using SSE for MCP communication");
            
            try {
                return objectMapper.writeValueAsString(info);
            } catch (Exception e) {
                logger.error("Error creating server info", e);
                return "{}";
            }
        });
    }

    /**
     * 健康检查端点
     */
    @GetMapping(value = "/health", produces = MediaType.APPLICATION_JSON_VALUE)
    public Mono<String> healthCheck() {
        return Mono.fromCallable(() -> {
            ObjectNode health = objectMapper.createObjectNode();
            health.put("status", "UP");
            health.put("timestamp", System.currentTimeMillis());
            
            try {
                return objectMapper.writeValueAsString(health);
            } catch (Exception e) {
                logger.error("Error creating health response", e);
                return "{\"status\":\"ERROR\"}";
            }
        });
    }

    private String handleRequest(JsonNode request) {
        String method = request.path("method").asText();
        JsonNode id = request.path("id");
        JsonNode params = request.path("params");
        
        logger.debug("Received MCP request: method={}, id={}", method, id);
        
        switch (method) {
            case "initialize":
                return handleInitialize(id, params);
            case "tools/list":
                return handleToolsList(id);
            case "tools/call":
                return handleToolCall(id, params);
            default:
                return createErrorResponse(id.asLong(-1), "Method not found", "Unknown method: " + method);
        }
    }

    private String handleInitialize(JsonNode id, JsonNode params) {
        ObjectNode response = objectMapper.createObjectNode();
        response.put("jsonrpc", "2.0");
        response.set("id", id);
        
        ObjectNode result = objectMapper.createObjectNode();
        result.put("protocolVersion", "2024-11-05");
        
        ObjectNode capabilities = objectMapper.createObjectNode();
        ObjectNode tools = objectMapper.createObjectNode();
        tools.put("listChanged", false);
        capabilities.set("tools", tools);
        result.set("capabilities", capabilities);
        
        ObjectNode serverInfo = objectMapper.createObjectNode();
        serverInfo.put("name", "Calculator MCP Server");
        serverInfo.put("version", "1.0.0");
        result.set("serverInfo", serverInfo);
        
        response.set("result", result);
        return writeValueAsString(response);
    }

    private String handleToolsList(JsonNode id) {
        ObjectNode response = objectMapper.createObjectNode();
        response.put("jsonrpc", "2.0");
        response.set("id", id);
        
        ObjectNode result = objectMapper.createObjectNode();
        ArrayNode tools = objectMapper.createArrayNode();
        
        // Add calculator tools
        tools.add(createTool("calculator_add", "Add two numbers", 
            new String[]{"a", "b"}, new String[]{"First number", "Second number"}));
        tools.add(createTool("calculator_subtract", "Subtract two numbers", 
            new String[]{"a", "b"}, new String[]{"First number", "Second number"}));
        tools.add(createTool("calculator_multiply", "Multiply two numbers", 
            new String[]{"a", "b"}, new String[]{"First number", "Second number"}));
        tools.add(createTool("calculator_divide", "Divide two numbers", 
            new String[]{"a", "b"}, new String[]{"Dividend", "Divisor"}));
        
        result.set("tools", tools);
        response.set("result", result);
        return writeValueAsString(response);
    }

    private ObjectNode createTool(String name, String description, String[] paramNames, String[] paramDescriptions) {
        ObjectNode tool = objectMapper.createObjectNode();
        tool.put("name", name);
        tool.put("description", description);
        
        ObjectNode inputSchema = objectMapper.createObjectNode();
        inputSchema.put("type", "object");
        
        ObjectNode properties = objectMapper.createObjectNode();
        ArrayNode required = objectMapper.createArrayNode();
        
        // 支持两种参数格式
        if (paramNames.length == 2 && "a".equals(paramNames[0]) && "b".equals(paramNames[1])) {
            // 传统的 a, b 参数格式
            for (int i = 0; i < paramNames.length; i++) {
                ObjectNode param = objectMapper.createObjectNode();
                param.put("type", "number");
                param.put("description", paramDescriptions[i]);
                properties.set(paramNames[i], param);
                required.add(paramNames[i]);
            }
            
            // 同时支持 numbers 数组格式
            ObjectNode numbersParam = objectMapper.createObjectNode();
            numbersParam.put("type", "array");
            numbersParam.put("description", "Array of numbers for calculation (alternative to a, b parameters)");
            
            ObjectNode itemsSchema = objectMapper.createObjectNode();
            itemsSchema.put("type", "number");
            numbersParam.set("items", itemsSchema);
            numbersParam.put("minItems", 2);
            
            properties.set("numbers", numbersParam);
            
            // 使用 anyOf 来支持两种格式
            ArrayNode anyOf = objectMapper.createArrayNode();
            
            // 格式1: a, b 参数
            ObjectNode format1 = objectMapper.createObjectNode();
            format1.put("type", "object");
            ObjectNode format1Props = objectMapper.createObjectNode();
            for (int i = 0; i < paramNames.length; i++) {
                ObjectNode param = objectMapper.createObjectNode();
                param.put("type", "number");
                param.put("description", paramDescriptions[i]);
                format1Props.set(paramNames[i], param);
            }
            format1.set("properties", format1Props);
            ArrayNode format1Required = objectMapper.createArrayNode();
            for (String paramName : paramNames) {
                format1Required.add(paramName);
            }
            format1.set("required", format1Required);
            anyOf.add(format1);
            
            // 格式2: numbers 数组
            ObjectNode format2 = objectMapper.createObjectNode();
            format2.put("type", "object");
            ObjectNode format2Props = objectMapper.createObjectNode();
            format2Props.set("numbers", numbersParam);
            format2.set("properties", format2Props);
            ArrayNode format2Required = objectMapper.createArrayNode();
            format2Required.add("numbers");
            format2.set("required", format2Required);
            anyOf.add(format2);
            
            inputSchema.set("anyOf", anyOf);
        } else {
            // 其他格式保持原样
            for (int i = 0; i < paramNames.length; i++) {
                ObjectNode param = objectMapper.createObjectNode();
                param.put("type", "number");
                param.put("description", paramDescriptions[i]);
                properties.set(paramNames[i], param);
                required.add(paramNames[i]);
            }
            inputSchema.set("properties", properties);
            inputSchema.set("required", required);
        }
        
        tool.set("inputSchema", inputSchema);
        return tool;
    }

    private String handleToolCall(JsonNode id, JsonNode params) {
        String toolName = params.path("name").asText();
        JsonNode arguments = params.path("arguments");
        
        try {
            String result = executeCalculation(toolName, arguments);
            return createToolResult(id, result);
        } catch (Exception e) {
            return createErrorResponse(id.asLong(-1), "Tool execution failed", e.getMessage());
        }
    }

    private String executeCalculation(String toolName, JsonNode arguments) throws Exception {
        // 支持两种参数格式：{a, b} 或 {numbers: [a, b, ...]}
        List<Double> numbers;
        
        if (arguments.has("numbers")) {
            // 新格式：使用numbers数组
            JsonNode numbersNode = arguments.path("numbers");
            numbers = new java.util.ArrayList<>();
            if (numbersNode.isArray()) {
                for (JsonNode numberNode : numbersNode) {
                    numbers.add(numberNode.asDouble());
                }
            } else {
                throw new IllegalArgumentException("'numbers' parameter must be an array");
            }
        } else if (arguments.has("a") && arguments.has("b")) {
            // 旧格式：使用a, b参数
            double a = arguments.path("a").asDouble();
            double b = arguments.path("b").asDouble();
            numbers = List.of(a, b);
        } else {
            throw new IllegalArgumentException("Missing required parameters. Expected 'numbers' array or 'a' and 'b' parameters");
        }
        
        // 使用计算器服务执行计算
        io.kt666.mcp.calcmcp.model.CalculationResponse response;
        
        switch (toolName) {
            case "calculator_add":
                response = calculatorService.add(numbers);
                break;
            case "calculator_subtract":
                response = calculatorService.subtract(numbers);
                break;
            case "calculator_multiply":
                response = calculatorService.multiply(numbers);
                break;
            case "calculator_divide":
                response = calculatorService.divide(numbers);
                break;
            default:
                throw new IllegalArgumentException("Unknown tool: " + toolName);
        }
        
        return response.getDescription();
    }

    private String createToolResult(JsonNode id, String result) {
        ObjectNode response = objectMapper.createObjectNode();
        response.put("jsonrpc", "2.0");
        response.set("id", id);
        
        ObjectNode resultObj = objectMapper.createObjectNode();
        ArrayNode content = objectMapper.createArrayNode();
        
        ObjectNode textContent = objectMapper.createObjectNode();
        textContent.put("type", "text");
        textContent.put("text", result);
        content.add(textContent);
        
        resultObj.set("content", content);
        response.set("result", resultObj);
        
        return writeValueAsString(response);
    }

    private String createErrorResponse(long id, String message, String details) {
        ObjectNode response = objectMapper.createObjectNode();
        response.put("jsonrpc", "2.0");
        response.put("id", id);
        
        ObjectNode error = objectMapper.createObjectNode();
        error.put("code", -32603);
        error.put("message", message);
        if (details != null) {
            error.put("data", details);
        }
        
        response.set("error", error);
        return writeValueAsString(response);
    }

    private String writeValueAsString(ObjectNode node) {
        try {
            return objectMapper.writeValueAsString(node);
        } catch (Exception e) {
            logger.error("Error serializing response", e);
            return "{\"error\":\"Serialization failed\"}";
        }
    }
}