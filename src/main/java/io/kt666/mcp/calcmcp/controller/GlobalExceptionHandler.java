package io.kt666.mcp.calcmcp.controller;

import io.kt666.mcp.calcmcp.exception.CalculationException;
import io.kt666.mcp.calcmcp.exception.DivisionByZeroException;
import io.kt666.mcp.calcmcp.exception.InvalidInputException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import reactor.core.publisher.Mono;

import java.util.HashMap;
import java.util.Map;

/**
 * 全局异常处理器
 * 处理计算错误并返回符合MCP协议的错误响应格式
 */
@RestControllerAdvice
public class GlobalExceptionHandler {

    private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    /**
     * 处理输入验证异常
     */
    @ExceptionHandler(InvalidInputException.class)
    public Mono<ResponseEntity<Map<String, Object>>> handleInvalidInputException(InvalidInputException ex) {
        logger.warn("Invalid input error: {}", ex.getMessage());
        
        Map<String, Object> errorResponse = createMcpErrorResponse(
                ex.getErrorCode(),
                ex.getMessage(),
                ex.getOperation(),
                HttpStatus.BAD_REQUEST
        );
        
        return Mono.just(ResponseEntity.badRequest().body(errorResponse));
    }

    /**
     * 处理除零异常
     */
    @ExceptionHandler(DivisionByZeroException.class)
    public Mono<ResponseEntity<Map<String, Object>>> handleDivisionByZeroException(DivisionByZeroException ex) {
        logger.warn("Division by zero error: {}", ex.getMessage());
        
        Map<String, Object> errorResponse = createMcpErrorResponse(
                "DIVISION_BY_ZERO",
                "Division by zero is not allowed",
                ex.getOperation(),
                HttpStatus.BAD_REQUEST
        );
        
        return Mono.just(ResponseEntity.badRequest().body(errorResponse));
    }

    /**
     * 处理通用计算异常
     */
    @ExceptionHandler(CalculationException.class)
    public Mono<ResponseEntity<Map<String, Object>>> handleCalculationException(CalculationException ex) {
        logger.error("Calculation error: {}", ex.getMessage());
        
        Map<String, Object> errorResponse = createMcpErrorResponse(
                ex.getErrorCode(),
                ex.getMessage(),
                ex.getOperation(),
                HttpStatus.BAD_REQUEST
        );
        
        return Mono.just(ResponseEntity.badRequest().body(errorResponse));
    }

    /**
     * 处理非法参数异常
     */
    @ExceptionHandler(IllegalArgumentException.class)
    public Mono<ResponseEntity<Map<String, Object>>> handleIllegalArgumentException(IllegalArgumentException ex) {
        logger.warn("Illegal argument error: {}", ex.getMessage());
        
        Map<String, Object> errorResponse = createMcpErrorResponse(
                "REQUEST_ERROR",
                ex.getMessage(),
                "tool_call",
                HttpStatus.BAD_REQUEST
        );
        
        return Mono.just(ResponseEntity.badRequest().body(errorResponse));
    }

    /**
     * 处理运行时异常
     */
    @ExceptionHandler(RuntimeException.class)
    public Mono<ResponseEntity<Map<String, Object>>> handleRuntimeException(RuntimeException ex) {
        logger.error("Runtime error: {}", ex.getMessage(), ex);
        
        Map<String, Object> errorResponse = createMcpErrorResponse(
                "RUNTIME_ERROR",
                "An unexpected error occurred: " + ex.getMessage(),
                "unknown",
                HttpStatus.INTERNAL_SERVER_ERROR
        );
        
        return Mono.just(ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse));
    }

    /**
     * 处理通用异常
     */
    @ExceptionHandler(Exception.class)
    public Mono<ResponseEntity<Map<String, Object>>> handleGenericException(Exception ex) {
        logger.error("Unexpected error: {}", ex.getMessage(), ex);
        
        Map<String, Object> errorResponse = createMcpErrorResponse(
                "INTERNAL_ERROR",
                "An internal server error occurred",
                "unknown",
                HttpStatus.INTERNAL_SERVER_ERROR
        );
        
        return Mono.just(ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse));
    }

    /**
     * 创建符合MCP协议的错误响应格式
     * 
     * @param errorCode 错误代码
     * @param message 错误消息
     * @param operation 操作名称
     * @param httpStatus HTTP状态码
     * @return 格式化的错误响应
     */
    private Map<String, Object> createMcpErrorResponse(String errorCode, String message, 
                                                      String operation, HttpStatus httpStatus) {
        Map<String, Object> error = new HashMap<>();
        error.put("code", errorCode);
        error.put("message", message);
        
        Map<String, Object> errorData = new HashMap<>();
        errorData.put("operation", operation);
        errorData.put("timestamp", System.currentTimeMillis());
        errorData.put("httpStatus", httpStatus.value());
        
        error.put("data", errorData);
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("error", error);
        
        return response;
    }
}