package io.kt666.mcp.calcmcp.model;

import java.util.List;

/**
 * 计算请求数据传输对象
 */
public class CalculationRequest {
    private String operation;
    private List<Double> numbers;

    public CalculationRequest() {}

    public CalculationRequest(String operation, List<Double> numbers) {
        this.operation = operation;
        this.numbers = numbers;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public List<Double> getNumbers() {
        return numbers;
    }

    public void setNumbers(List<Double> numbers) {
        this.numbers = numbers;
    }

    @Override
    public String toString() {
        return "CalculationRequest{" +
                "operation='" + operation + '\'' +
                ", numbers=" + numbers +
                '}';
    }
}