package io.kt666.mcp.calcmcp.model;

import java.util.List;

/**
 * 计算响应数据传输对象
 */
public class CalculationResponse {
    private Double result;
    private String operation;
    private List<Double> operands;
    private String description;

    public CalculationResponse() {}

    public CalculationResponse(Double result, String operation, List<Double> operands, String description) {
        this.result = result;
        this.operation = operation;
        this.operands = operands;
        this.description = description;
    }

    public Double getResult() {
        return result;
    }

    public void setResult(Double result) {
        this.result = result;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public List<Double> getOperands() {
        return operands;
    }

    public void setOperands(List<Double> operands) {
        this.operands = operands;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    public String toString() {
        return "CalculationResponse{" +
                "result=" + result +
                ", operation='" + operation + '\'' +
                ", operands=" + operands +
                ", description='" + description + '\'' +
                '}';
    }
}