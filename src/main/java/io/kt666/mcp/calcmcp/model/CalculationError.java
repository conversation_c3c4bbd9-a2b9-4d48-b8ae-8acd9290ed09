package io.kt666.mcp.calcmcp.model;

/**
 * 计算错误数据传输对象
 */
public class CalculationError {
    private String errorCode;
    private String message;
    private String operation;

    public CalculationError() {}

    public CalculationError(String errorCode, String message, String operation) {
        this.errorCode = errorCode;
        this.message = message;
        this.operation = operation;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    @Override
    public String toString() {
        return "CalculationError{" +
                "errorCode='" + errorCode + '\'' +
                ", message='" + message + '\'' +
                ", operation='" + operation + '\'' +
                '}';
    }
}