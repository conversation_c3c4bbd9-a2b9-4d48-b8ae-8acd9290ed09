package io.kt666.mcp.calcmcp.service;

import io.kt666.mcp.calcmcp.model.CalculationResponse;

import java.util.List;

/**
 * 计算器服务接口
 * 提供基本的数学运算功能
 */
public interface CalculatorService {
    
    /**
     * 执行加法运算
     * 计算两个或多个数字的和
     * 
     * @param numbers 参与加法运算的数字列表，至少包含2个元素
     * @return 计算结果响应对象，包含结果值和操作描述
     * @throws io.kt666.mcp.calcmcp.exception.InvalidInputException 当输入无效时
     * 
     * 示例：
     * - add([1.0, 2.0]) 返回 3.0
     * - add([1.5, 2.5, 3.0]) 返回 7.0
     * - add([-1.0, 1.0]) 返回 0.0
     */
    CalculationResponse add(List<Double> numbers);
    
    /**
     * 执行减法运算
     * 按顺序执行减法：第一个数字减去后续所有数字
     * 
     * @param numbers 参与减法运算的数字列表，至少包含2个元素
     * @return 计算结果响应对象，包含结果值和操作描述
     * @throws io.kt666.mcp.calcmcp.exception.InvalidInputException 当输入无效时
     * 
     * 示例：
     * - subtract([10.0, 3.0]) 返回 7.0
     * - subtract([10.0, 3.0, 2.0]) 返回 5.0 (10 - 3 - 2)
     * - subtract([5.0, 10.0]) 返回 -5.0
     */
    CalculationResponse subtract(List<Double> numbers);
    
    /**
     * 执行乘法运算
     * 计算两个或多个数字的乘积
     * 
     * @param numbers 参与乘法运算的数字列表，至少包含2个元素
     * @return 计算结果响应对象，包含结果值和操作描述
     * @throws io.kt666.mcp.calcmcp.exception.InvalidInputException 当输入无效时
     * 
     * 示例：
     * - multiply([2.0, 3.0]) 返回 6.0
     * - multiply([2.0, 3.0, 4.0]) 返回 24.0
     * - multiply([5.0, 0.0]) 返回 0.0
     */
    CalculationResponse multiply(List<Double> numbers);
    
    /**
     * 执行除法运算
     * 按顺序执行除法：第一个数字除以后续所有数字
     * 
     * @param numbers 参与除法运算的数字列表，至少包含2个元素
     * @return 计算结果响应对象，包含结果值和操作描述
     * @throws io.kt666.mcp.calcmcp.exception.InvalidInputException 当输入无效时
     * @throws io.kt666.mcp.calcmcp.exception.DivisionByZeroException 当除数为零时
     * 
     * 示例：
     * - divide([10.0, 2.0]) 返回 5.0
     * - divide([20.0, 2.0, 2.0]) 返回 5.0 (20 / 2 / 2)
     * - divide([7.0, 2.0]) 返回 3.5
     */
    CalculationResponse divide(List<Double> numbers);
}