package io.kt666.mcp.calcmcp.service.impl;

import io.kt666.mcp.calcmcp.model.CalculationResponse;
import io.kt666.mcp.calcmcp.service.CalculatorService;
import io.kt666.mcp.calcmcp.service.InputValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 计算器服务实现类
 * 实现基本的数学运算功能
 */
@Service
public class CalculatorServiceImpl implements CalculatorService {

    private final InputValidator inputValidator;

    @Autowired
    public CalculatorServiceImpl(InputValidator inputValidator) {
        this.inputValidator = inputValidator;
    }

    @Override
    public CalculationResponse add(List<Double> numbers) {
        inputValidator.validateNumbers(numbers);
        
        double result = numbers.stream()
                .mapToDouble(Double::doubleValue)
                .sum();
        
        String description = String.format("Added %d numbers: %s = %.2f", 
                numbers.size(), formatNumbersList(numbers), result);
        
        return new CalculationResponse(result, "add", numbers, description);
    }

    @Override
    public CalculationResponse subtract(List<Double> numbers) {
        inputValidator.validateNumbers(numbers);
        
        double result = numbers.get(0);
        for (int i = 1; i < numbers.size(); i++) {
            result -= numbers.get(i);
        }
        
        String description = String.format("Subtracted %d numbers: %s = %.2f", 
                numbers.size(), formatNumbersList(numbers), result);
        
        return new CalculationResponse(result, "subtract", numbers, description);
    }

    @Override
    public CalculationResponse multiply(List<Double> numbers) {
        inputValidator.validateNumbers(numbers);
        
        double result = numbers.stream()
                .mapToDouble(Double::doubleValue)
                .reduce(1.0, (a, b) -> a * b);
        
        String description = String.format("Multiplied %d numbers: %s = %.2f", 
                numbers.size(), formatNumbersList(numbers), result);
        
        return new CalculationResponse(result, "multiply", numbers, description);
    }

    @Override
    public CalculationResponse divide(List<Double> numbers) {
        inputValidator.validateNumbers(numbers);
        inputValidator.validateDivisionByZero(numbers);
        
        double result = numbers.get(0);
        for (int i = 1; i < numbers.size(); i++) {
            result /= numbers.get(i);
        }
        
        String description = String.format("Divided %d numbers: %s = %.2f", 
                numbers.size(), formatNumbersList(numbers), result);
        
        return new CalculationResponse(result, "divide", numbers, description);
    }

    /**
     * 格式化数字列表为字符串表示
     * @param numbers 数字列表
     * @return 格式化后的字符串
     */
    private String formatNumbersList(List<Double> numbers) {
        if (numbers == null || numbers.isEmpty()) {
            return "[]";
        }
        
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < numbers.size(); i++) {
            if (i > 0) {
                sb.append(", ");
            }
            sb.append(String.format("%.2f", numbers.get(i)));
        }
        
        return sb.toString();
    }
}