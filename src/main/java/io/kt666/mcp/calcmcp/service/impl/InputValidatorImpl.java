package io.kt666.mcp.calcmcp.service.impl;

import io.kt666.mcp.calcmcp.exception.DivisionByZeroException;
import io.kt666.mcp.calcmcp.exception.InvalidInputException;
import io.kt666.mcp.calcmcp.service.InputValidator;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 输入验证服务实现类
 */
@Service
public class InputValidatorImpl implements InputValidator {

    @Override
    public void validateNumbers(List<Double> numbers) {
        validateNotEmpty(numbers);
        validateNumberRange(numbers);
        
        // 检查是否包含null值
        for (int i = 0; i < numbers.size(); i++) {
            Double number = numbers.get(i);
            if (number == null) {
                throw new InvalidInputException(
                    "Number at position " + i + " is null", 
                    "validation"
                );
            }
        }
    }

    @Override
    public void validateNotEmpty(List<Double> numbers) {
        if (numbers == null) {
            throw new InvalidInputException("Numbers list cannot be null", "validation");
        }
        
        if (numbers.isEmpty()) {
            throw new InvalidInputException("Numbers list cannot be empty", "validation");
        }
        
        if (numbers.size() < 2) {
            throw new InvalidInputException(
                "At least 2 numbers are required for calculation, but got " + numbers.size(), 
                "validation"
            );
        }
    }

    @Override
    public void validateDivisionByZero(List<Double> numbers) {
        validateNotEmpty(numbers);
        
        // 检查除第一个数字外的所有数字（作为除数）是否为零
        for (int i = 1; i < numbers.size(); i++) {
            Double divisor = numbers.get(i);
            if (divisor != null && divisor.equals(0.0)) {
                throw new DivisionByZeroException("divide");
            }
        }
    }

    @Override
    public void validateNumberRange(List<Double> numbers) {
        if (numbers == null) {
            return;
        }
        
        for (int i = 0; i < numbers.size(); i++) {
            Double number = numbers.get(i);
            if (number != null) {
                // 检查是否为NaN
                if (Double.isNaN(number)) {
                    throw new InvalidInputException(
                        "Number at position " + i + " is NaN", 
                        "validation"
                    );
                }
                
                // 检查是否为无穷大
                if (Double.isInfinite(number)) {
                    throw new InvalidInputException(
                        "Number at position " + i + " is infinite", 
                        "validation"
                    );
                }
            }
        }
    }
}