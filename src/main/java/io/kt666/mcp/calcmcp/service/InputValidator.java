package io.kt666.mcp.calcmcp.service;

import java.util.List;

/**
 * 输入验证服务接口
 */
public interface InputValidator {
    
    /**
     * 验证数字列表的有效性
     * @param numbers 待验证的数字列表
     * @throws io.kt666.mcp.calcmcp.exception.InvalidInputException 当输入无效时
     */
    void validateNumbers(List<Double> numbers);
    
    /**
     * 验证列表不为空且包含足够的元素
     * @param numbers 待验证的数字列表
     * @throws io.kt666.mcp.calcmcp.exception.InvalidInputException 当列表为空或元素不足时
     */
    void validateNotEmpty(List<Double> numbers);
    
    /**
     * 验证除法运算中除数不为零
     * @param numbers 待验证的数字列表，第二个及后续元素作为除数
     * @throws io.kt666.mcp.calcmcp.exception.DivisionByZeroException 当存在零除数时
     */
    void validateDivisionByZero(List<Double> numbers);
    
    /**
     * 验证数值是否在有效范围内
     * @param numbers 待验证的数字列表
     * @throws io.kt666.mcp.calcmcp.exception.InvalidInputException 当数值超出范围时
     */
    void validateNumberRange(List<Double> numbers);
}