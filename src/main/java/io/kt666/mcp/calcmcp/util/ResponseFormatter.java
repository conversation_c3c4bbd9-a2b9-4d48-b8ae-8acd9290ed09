package io.kt666.mcp.calcmcp.util;

import io.kt666.mcp.calcmcp.model.CalculationResponse;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 响应格式化工具类
 * 提供标准化的MCP响应格式
 */
@Component
public class ResponseFormatter {

    /**
     * 格式化成功的计算响应
     * 
     * @param calculationResponse 计算结果
     * @return 格式化的响应Map
     */
    public Map<String, Object> formatSuccessResponse(CalculationResponse calculationResponse) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("result", calculationResponse.getResult());
        response.put("operation", calculationResponse.getOperation());
        response.put("operands", calculationResponse.getOperands());
        response.put("description", calculationResponse.getDescription());
        response.put("timestamp", System.currentTimeMillis());
        
        return response;
    }

    /**
     * 格式化成功响应（通用版本）
     * 
     * @param result 计算结果
     * @param operation 操作类型
     * @param operands 操作数
     * @param description 描述
     * @return 格式化的响应Map
     */
    public Map<String, Object> formatSuccessResponse(Double result, String operation, 
                                                   List<Double> operands, String description) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("result", result);
        response.put("operation", operation);
        response.put("operands", operands);
        response.put("description", description);
        response.put("timestamp", System.currentTimeMillis());
        
        return response;
    }

    /**
     * 格式化错误响应
     * 
     * @param errorCode 错误代码
     * @param errorMessage 错误消息
     * @param operation 操作类型
     * @return 格式化的错误响应Map
     */
    public Map<String, Object> formatErrorResponse(String errorCode, String errorMessage, String operation) {
        Map<String, Object> error = new HashMap<>();
        error.put("code", errorCode);
        error.put("message", errorMessage);
        
        Map<String, Object> errorData = new HashMap<>();
        errorData.put("operation", operation);
        errorData.put("timestamp", System.currentTimeMillis());
        
        error.put("data", errorData);
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("error", error);
        
        return response;
    }

    /**
     * 格式化工具列表响应
     * 
     * @param tools 工具定义Map
     * @param serverInfo 服务器信息
     * @return 格式化的工具列表响应
     */
    public Map<String, Object> formatToolListResponse(Map<String, Object> tools, Map<String, Object> serverInfo) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("tools", tools);
        response.put("server", serverInfo);
        response.put("timestamp", System.currentTimeMillis());
        response.put("toolCount", tools.size());
        
        return response;
    }

    /**
     * 格式化单个工具定义响应
     * 
     * @param toolName 工具名称
     * @param toolDefinition 工具定义
     * @return 格式化的工具定义响应
     */
    public Map<String, Object> formatToolDefinitionResponse(String toolName, Map<String, Object> toolDefinition) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("toolName", toolName);
        response.put("tool", toolDefinition);
        response.put("timestamp", System.currentTimeMillis());
        
        return response;
    }

    /**
     * 格式化服务器信息响应
     * 
     * @param serverInfo 服务器信息
     * @return 格式化的服务器信息响应
     */
    public Map<String, Object> formatServerInfoResponse(Map<String, Object> serverInfo) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("server", serverInfo);
        response.put("timestamp", System.currentTimeMillis());
        
        return response;
    }

    /**
     * 格式化健康检查响应
     * 
     * @param availableTools 可用工具列表
     * @return 格式化的健康检查响应
     */
    public Map<String, Object> formatHealthCheckResponse(java.util.Set<String> availableTools) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("status", "UP");
        response.put("service", "Calculator MCP Server");
        response.put("timestamp", System.currentTimeMillis());
        response.put("availableTools", availableTools);
        response.put("toolCount", availableTools.size());
        
        return response;
    }
}