#!/usr/bin/env python3
"""
Calculator MCP Server SSE 模式测试脚本

这个脚本用于测试Calculator MCP Server的SSE模式功能，
包括连接测试、MCP协议测试和计算器功能测试。
"""

import json
import time
import asyncio
import aiohttp
import sseclient
import requests
from typing import Dict, Any, Optional

class McpSseClient:
    """MCP SSE客户端"""
    
    def __init__(self, base_url: str = "http://localhost:8080"):
        self.base_url = base_url
        self.request_id = 1
        self.session = None
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    def get_next_id(self) -> int:
        """获取下一个请求ID"""
        current_id = self.request_id
        self.request_id += 1
        return current_id
    
    async def send_mcp_request(self, method: str, params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """发送MCP请求"""
        request_data = {
            "jsonrpc": "2.0",
            "id": self.get_next_id(),
            "method": method
        }
        
        if params:
            request_data["params"] = params
            
        print(f"发送MCP请求: {method}")
        print(f"请求数据: {json.dumps(request_data, indent=2, ensure_ascii=False)}")
        
        async with self.session.post(
            f"{self.base_url}/mcp/message",
            json=request_data,
            headers={"Content-Type": "application/json"}
        ) as response:
            if response.status == 200:
                result = await response.json()
                print(f"响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
                return result
            else:
                error_text = await response.text()
                print(f"请求失败: {response.status} - {error_text}")
                raise Exception(f"HTTP {response.status}: {error_text}")
    
    async def get_server_info(self) -> Dict[str, Any]:
        """获取服务器信息"""
        async with self.session.get(f"{self.base_url}/mcp/info") as response:
            return await response.json()
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        async with self.session.get(f"{self.base_url}/mcp/health") as response:
            return await response.json()
    
    def test_sse_connection(self, duration: int = 10):
        """测试SSE连接"""
        print(f"\n=== 测试SSE连接 (持续{duration}秒) ===")
        
        try:
            response = requests.get(f"{self.base_url}/mcp/sse", stream=True)
            client = sseclient.SSEClient(response)
            
            start_time = time.time()
            message_count = 0
            
            for event in client.events():
                current_time = time.time()
                if current_time - start_time > duration:
                    break
                    
                message_count += 1
                print(f"收到SSE事件: {event.event} - {event.data}")
                
            print(f"SSE测试完成，共收到 {message_count} 条消息")
            return True
            
        except Exception as e:
            print(f"SSE连接测试失败: {e}")
            return False

async def test_basic_functionality():
    """测试基本功能"""
    print("\n=== 基本功能测试 ===")
    
    async with McpSseClient() as client:
        try:
            # 1. 健康检查
            print("\n1. 健康检查")
            health = await client.health_check()
            print(f"健康状态: {health}")
            
            # 2. 服务器信息
            print("\n2. 服务器信息")
            info = await client.get_server_info()
            print(f"服务器信息: {json.dumps(info, indent=2, ensure_ascii=False)}")
            
            return True
            
        except Exception as e:
            print(f"基本功能测试失败: {e}")
            return False

async def test_mcp_protocol():
    """测试MCP协议"""
    print("\n=== MCP协议测试 ===")
    
    async with McpSseClient() as client:
        try:
            # 1. Initialize
            print("\n1. Initialize")
            init_result = await client.send_mcp_request("initialize", {
                "protocolVersion": "2024-11-05",
                "capabilities": {},
                "clientInfo": {
                    "name": "Python Test Client",
                    "version": "1.0.0"
                }
            })
            
            if "result" not in init_result:
                print("Initialize失败")
                return False
            
            # 2. Tools List
            print("\n2. Tools List")
            tools_result = await client.send_mcp_request("tools/list")
            
            if "result" not in tools_result:
                print("Tools List失败")
                return False
                
            tools = tools_result["result"].get("tools", [])
            print(f"可用工具数量: {len(tools)}")
            
            return True
            
        except Exception as e:
            print(f"MCP协议测试失败: {e}")
            return False

async def test_calculator_operations():
    """测试计算器操作"""
    print("\n=== 计算器操作测试 ===")
    
    test_cases = [
        ("calculator_add", {"a": 10, "b": 5}, "10 + 5 = 15"),
        ("calculator_subtract", {"a": 10, "b": 3}, "10 - 3 = 7"),
        ("calculator_multiply", {"a": 4, "b": 6}, "4 × 6 = 24"),
        ("calculator_divide", {"a": 20, "b": 4}, "20 ÷ 4 = 5"),
        ("calculator_divide", {"a": 7, "b": 2}, "7 ÷ 2 = 3.5"),
    ]
    
    async with McpSseClient() as client:
        success_count = 0
        
        for tool_name, args, expected_desc in test_cases:
            try:
                print(f"\n测试: {tool_name} with {args}")
                
                result = await client.send_mcp_request("tools/call", {
                    "name": tool_name,
                    "arguments": args
                })
                
                if "result" in result and "content" in result["result"]:
                    content = result["result"]["content"]
                    if content and len(content) > 0:
                        text_result = content[0].get("text", "")
                        print(f"计算结果: {text_result}")
                        success_count += 1
                    else:
                        print("结果格式错误")
                else:
                    print(f"计算失败: {result}")
                    
            except Exception as e:
                print(f"计算错误: {e}")
        
        print(f"\n计算器测试完成: {success_count}/{len(test_cases)} 成功")
        return success_count == len(test_cases)

async def test_error_handling():
    """测试错误处理"""
    print("\n=== 错误处理测试 ===")
    
    error_test_cases = [
        ("calculator_divide", {"a": 10, "b": 0}, "除零错误"),
        ("calculator_unknown", {"a": 1, "b": 2}, "未知工具"),
        ("invalid_method", {}, "无效方法"),
    ]
    
    async with McpSseClient() as client:
        error_handled_count = 0
        
        for tool_name, args, error_desc in error_test_cases:
            try:
                print(f"\n测试错误情况: {error_desc}")
                
                if tool_name == "invalid_method":
                    result = await client.send_mcp_request(tool_name, args)
                else:
                    result = await client.send_mcp_request("tools/call", {
                        "name": tool_name,
                        "arguments": args
                    })
                
                if "error" in result:
                    print(f"正确处理错误: {result['error']}")
                    error_handled_count += 1
                else:
                    print(f"未正确处理错误: {result}")
                    
            except Exception as e:
                print(f"异常处理: {e}")
                error_handled_count += 1
        
        print(f"\n错误处理测试完成: {error_handled_count}/{len(error_test_cases)} 正确处理")
        return error_handled_count >= len(error_test_cases) - 1  # 允许一个测试失败

async def run_performance_test():
    """性能测试"""
    print("\n=== 性能测试 ===")
    
    async with McpSseClient() as client:
        # 并发请求测试
        concurrent_requests = 10
        start_time = time.time()
        
        tasks = []
        for i in range(concurrent_requests):
            task = client.send_mcp_request("tools/call", {
                "name": "calculator_add",
                "arguments": {"a": i, "b": i + 1}
            })
            tasks.append(task)
        
        try:
            results = await asyncio.gather(*tasks, return_exceptions=True)
            end_time = time.time()
            
            success_count = sum(1 for r in results if not isinstance(r, Exception))
            total_time = end_time - start_time
            
            print(f"并发请求测试: {success_count}/{concurrent_requests} 成功")
            print(f"总耗时: {total_time:.2f}秒")
            print(f"平均响应时间: {total_time/concurrent_requests:.3f}秒")
            
            return success_count >= concurrent_requests * 0.8  # 80%成功率
            
        except Exception as e:
            print(f"性能测试失败: {e}")
            return False

def check_server_availability():
    """检查服务器是否可用"""
    try:
        response = requests.get("http://localhost:8080/mcp/health", timeout=5)
        return response.status_code == 200
    except:
        return False

async def main():
    """主测试函数"""
    print("Calculator MCP Server SSE 模式测试")
    print("=" * 50)
    
    # 检查服务器是否启动
    if not check_server_availability():
        print("❌ 服务器未启动或不可访问")
        print("请先启动服务器: java -jar target/calcMcp-0.0.1-SNAPSHOT.jar --transport=sse")
        return
    
    print("✅ 服务器可访问")
    
    # 运行测试
    test_results = []
    
    # 1. 基本功能测试
    result = await test_basic_functionality()
    test_results.append(("基本功能", result))
    
    # 2. MCP协议测试
    result = await test_mcp_protocol()
    test_results.append(("MCP协议", result))
    
    # 3. 计算器操作测试
    result = await test_calculator_operations()
    test_results.append(("计算器操作", result))
    
    # 4. 错误处理测试
    result = await test_error_handling()
    test_results.append(("错误处理", result))
    
    # 5. 性能测试
    result = await run_performance_test()
    test_results.append(("性能测试", result))
    
    # 6. SSE连接测试 (同步)
    client = McpSseClient()
    result = client.test_sse_connection(duration=5)
    test_results.append(("SSE连接", result))
    
    # 输出测试结果
    print("\n" + "=" * 50)
    print("测试结果汇总:")
    print("=" * 50)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:15} {status}")
        if result:
            passed += 1
    
    print("-" * 50)
    print(f"总计: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！")
    else:
        print("⚠️  部分测试失败，请检查服务器状态")

if __name__ == "__main__":
    # 安装依赖提示
    try:
        import sseclient
        import aiohttp
    except ImportError as e:
        print("缺少依赖包，请安装:")
        print("pip install sseclient-py aiohttp requests")
        exit(1)
    
    asyncio.run(main())
async
 def test_multi_number_calculations():
    """测试多数字计算"""
    print("\n=== 多数字计算测试 ===")
    
    test_cases = [
        ("calculator_add", {"numbers": [1, 2, 3, 4]}, "1 + 2 + 3 + 4 = 10"),
        ("calculator_subtract", {"numbers": [20, 5, 3, 2]}, "20 - 5 - 3 - 2 = 10"),
        ("calculator_multiply", {"numbers": [2, 3, 4]}, "2 × 3 × 4 = 24"),
        ("calculator_divide", {"numbers": [100, 2, 5]}, "100 ÷ 2 ÷ 5 = 10"),
        ("calculator_add", {"numbers": [1.5, 2.5, 3.0]}, "1.5 + 2.5 + 3.0 = 7.0"),
    ]
    
    async with McpSseClient() as client:
        success_count = 0
        
        for tool_name, args, expected_desc in test_cases:
            try:
                print(f"\n测试多数字计算: {tool_name} with {args}")
                
                result = await client.send_mcp_request("tools/call", {
                    "name": tool_name,
                    "arguments": args
                })
                
                if "result" in result and "content" in result["result"]:
                    content = result["result"]["content"]
                    if content and len(content) > 0:
                        text_result = content[0].get("text", "")
                        print(f"计算结果: {text_result}")
                        success_count += 1
                    else:
                        print("结果格式错误")
                else:
                    print(f"计算失败: {result}")
                    
            except Exception as e:
                print(f"计算错误: {e}")
        
        print(f"\n多数字计算测试完成: {success_count}/{len(test_cases)} 成功")
        return success_count == len(test_cases)

async def test_backward_compatibility():
    """测试向后兼容性（a, b参数格式）"""
    print("\n=== 向后兼容性测试 ===")
    
    test_cases = [
        ("calculator_add", {"a": 15, "b": 25}, "15 + 25 = 40"),
        ("calculator_subtract", {"a": 50, "b": 20}, "50 - 20 = 30"),
        ("calculator_multiply", {"a": 6, "b": 7}, "6 × 7 = 42"),
        ("calculator_divide", {"a": 84, "b": 12}, "84 ÷ 12 = 7"),
    ]
    
    async with McpSseClient() as client:
        success_count = 0
        
        for tool_name, args, expected_desc in test_cases:
            try:
                print(f"\n测试向后兼容: {tool_name} with {args}")
                
                result = await client.send_mcp_request("tools/call", {
                    "name": tool_name,
                    "arguments": args
                })
                
                if "result" in result and "content" in result["result"]:
                    content = result["result"]["content"]
                    if content and len(content) > 0:
                        text_result = content[0].get("text", "")
                        print(f"计算结果: {text_result}")
                        success_count += 1
                    else:
                        print("结果格式错误")
                else:
                    print(f"计算失败: {result}")
                    
            except Exception as e:
                print(f"计算错误: {e}")
        
        print(f"\n向后兼容性测试完成: {success_count}/{len(test_cases)} 成功")
        return success_count == len(test_cases)

# 更新主函数，添加新的测试
async def main_extended():
    """扩展的主测试函数"""
    print("Calculator MCP Server SSE 模式完整测试")
    print("=" * 60)
    
    # 检查服务器是否启动
    if not check_server_availability():
        print("❌ 服务器未启动或不可访问")
        print("请先启动服务器: java -jar target/calcMcp-0.0.1-SNAPSHOT.jar --transport=sse")
        return
    
    print("✅ 服务器可访问")
    
    # 运行测试
    test_results = []
    
    # 1. 基本功能测试
    result = await test_basic_functionality()
    test_results.append(("基本功能", result))
    
    # 2. MCP协议测试
    result = await test_mcp_protocol()
    test_results.append(("MCP协议", result))
    
    # 3. 计算器操作测试（两个数字）
    result = await test_calculator_operations()
    test_results.append(("基础计算器操作", result))
    
    # 4. 多数字计算测试
    result = await test_multi_number_calculations()
    test_results.append(("多数字计算", result))
    
    # 5. 向后兼容性测试
    result = await test_backward_compatibility()
    test_results.append(("向后兼容性", result))
    
    # 6. 错误处理测试
    result = await test_error_handling()
    test_results.append(("错误处理", result))
    
    # 7. 性能测试
    result = await run_performance_test()
    test_results.append(("性能测试", result))
    
    # 8. SSE连接测试 (同步)
    client = McpSseClient()
    result = client.test_sse_connection(duration=5)
    test_results.append(("SSE连接", result))
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("测试结果汇总:")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:20} {status}")
        if result:
            passed += 1
    
    print("-" * 60)
    print(f"总计: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！Calculator MCP Server SSE模式工作正常")
    else:
        print("⚠️  部分测试失败，请检查服务器状态和日志")
        
    # 提供使用建议
    print("\n" + "=" * 60)
    print("使用建议:")
    print("=" * 60)
    print("1. 在Kiro中配置MCP服务器:")
    print("   - 编辑 .kiro/settings/mcp.json")
    print("   - 启用 calculator-sse 服务器")
    print("   - 设置 disabled: false")
    print("")
    print("2. 测试Web界面:")
    print("   - 打开 test-sse-simple.html")
    print("   - 在浏览器中进行交互测试")
    print("")
    print("3. API端点:")
    print("   - SSE: http://localhost:8080/mcp/sse")
    print("   - 消息: http://localhost:8080/mcp/message")
    print("   - 健康检查: http://localhost:8080/mcp/health")

if __name__ == "__main__":
    # 检查是否要运行扩展测试
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == "--extended":
        asyncio.run(main_extended())
    else:
        asyncio.run(main())