# Calculator MCP Server Dockerfile

# 使用官方OpenJDK 21镜像作为基础镜像
FROM openjdk:21-jdk-slim

# 设置维护者信息
LABEL maintainer="Calculator MCP Server Team"
LABEL description="A simple calculator service providing basic arithmetic operations via MCP protocol"
LABEL version="1.0.0"

# 设置工作目录
WORKDIR /app

# 创建非root用户
RUN groupadd -r mcpuser && useradd -r -g mcpuser mcpuser

# 安装必要的工具
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 创建应用目录结构
RUN mkdir -p /app/logs && \
    mkdir -p /app/config && \
    chown -R mcpuser:mcpuser /app

# 复制JAR文件
COPY target/calcMcp-0.0.1-SNAPSHOT.jar /app/calculator-mcp-server.jar

# 复制配置文件
COPY src/main/resources/application.yml /app/config/

# 设置文件权限
RUN chown mcpuser:mcpuser /app/calculator-mcp-server.jar && \
    chown -R mcpuser:mcpuser /app/config

# 切换到非root用户
USER mcpuser

# 暴露端口
EXPOSE 8080

# 设置环境变量
ENV JAVA_OPTS="-Xms256m -Xmx512m -XX:+UseG1GC -XX:MaxGCPauseMillis=200"
ENV SPRING_PROFILES_ACTIVE=prod
ENV SERVER_PORT=8080

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:${SERVER_PORT}/mcp/health || exit 1

# 启动命令
ENTRYPOINT ["sh", "-c", "java $JAVA_OPTS -jar /app/calculator-mcp-server.jar --spring.profiles.active=$SPRING_PROFILES_ACTIVE --server.port=$SERVER_PORT"]