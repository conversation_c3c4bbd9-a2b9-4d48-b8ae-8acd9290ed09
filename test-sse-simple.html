<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Calculator MCP Server - SSE Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        input[type="number"] {
            width: 80px;
            padding: 5px;
            margin: 2px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.connected {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.disconnected {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .result {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Calculator MCP Server - SSE Test</h1>
        
        <div class="section">
            <h3>连接状态</h3>
            <div id="status" class="status disconnected">未连接</div>
            <button id="connectBtn" onclick="connectSSE()">连接 SSE</button>
            <button id="disconnectBtn" onclick="disconnectSSE()" disabled>断开连接</button>
        </div>

        <div class="section">
            <h3>服务器信息</h3>
            <button onclick="getServerInfo()">获取服务器信息</button>
            <button onclick="healthCheck()">健康检查</button>
            <div id="serverInfo"></div>
        </div>

        <div class="section">
            <h3>计算器测试</h3>
            <div>
                <input type="number" id="num1" value="10" step="0.01"> 
                <select id="operation">
                    <option value="add">+</option>
                    <option value="subtract">-</option>
                    <option value="multiply">×</option>
                    <option value="divide">÷</option>
                </select>
                <input type="number" id="num2" value="5" step="0.01">
                <button onclick="calculate()">计算</button>
            </div>
            <div id="result"></div>
        </div>

        <div class="section">
            <h3>MCP 协议测试</h3>
            <button onclick="testInitialize()">Initialize</button>
            <button onclick="testToolsList()">Tools List</button>
            <button onclick="testToolCall()">Tool Call (Add)</button>
        </div>

        <div class="section">
            <h3>日志</h3>
            <button onclick="clearLog()">清空日志</button>
            <div id="log" class="log"></div>
        </div>
    </div>

    <script>
        let eventSource = null;
        let requestId = 1;

        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        function updateStatus(connected) {
            const statusDiv = document.getElementById('status');
            const connectBtn = document.getElementById('connectBtn');
            const disconnectBtn = document.getElementById('disconnectBtn');
            
            if (connected) {
                statusDiv.textContent = '已连接';
                statusDiv.className = 'status connected';
                connectBtn.disabled = true;
                disconnectBtn.disabled = false;
            } else {
                statusDiv.textContent = '未连接';
                statusDiv.className = 'status disconnected';
                connectBtn.disabled = false;
                disconnectBtn.disabled = true;
            }
        }

        function connectSSE() {
            if (eventSource) {
                eventSource.close();
            }

            log('正在连接 SSE...');
            eventSource = new EventSource('http://localhost:8080/mcp/sse');

            eventSource.onopen = function(event) {
                log('SSE 连接已建立');
                updateStatus(true);
            };

            eventSource.onmessage = function(event) {
                log('收到消息: ' + event.data);
            };

            eventSource.addEventListener('ping', function(event) {
                log('收到心跳: ' + event.data);
            });

            eventSource.onerror = function(event) {
                log('SSE 连接错误: ' + JSON.stringify(event));
                updateStatus(false);
            };

            eventSource.onclose = function(event) {
                log('SSE 连接已关闭');
                updateStatus(false);
            };
        }

        function disconnectSSE() {
            if (eventSource) {
                eventSource.close();
                eventSource = null;
                log('SSE 连接已断开');
                updateStatus(false);
            }
        }

        async function sendMcpRequest(method, params = {}) {
            const request = {
                jsonrpc: "2.0",
                id: requestId++,
                method: method,
                params: params
            };

            log('发送 MCP 请求: ' + JSON.stringify(request));

            try {
                const response = await fetch('http://localhost:8080/mcp/message', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(request)
                });

                const result = await response.text();
                log('收到 MCP 响应: ' + result);
                return JSON.parse(result);
            } catch (error) {
                log('MCP 请求错误: ' + error.message);
                throw error;
            }
        }

        async function getServerInfo() {
            try {
                const response = await fetch('http://localhost:8080/mcp/info');
                const info = await response.text();
                log('服务器信息: ' + info);
                document.getElementById('serverInfo').innerHTML = `<pre>${info}</pre>`;
            } catch (error) {
                log('获取服务器信息失败: ' + error.message);
            }
        }

        async function healthCheck() {
            try {
                const response = await fetch('http://localhost:8080/mcp/health');
                const health = await response.text();
                log('健康检查: ' + health);
            } catch (error) {
                log('健康检查失败: ' + error.message);
            }
        }

        async function testInitialize() {
            try {
                const result = await sendMcpRequest('initialize', {
                    protocolVersion: "2024-11-05",
                    capabilities: {},
                    clientInfo: {
                        name: "Test Client",
                        version: "1.0.0"
                    }
                });
                log('Initialize 成功: ' + JSON.stringify(result));
            } catch (error) {
                log('Initialize 失败: ' + error.message);
            }
        }

        async function testToolsList() {
            try {
                const result = await sendMcpRequest('tools/list');
                log('Tools List 成功: ' + JSON.stringify(result));
            } catch (error) {
                log('Tools List 失败: ' + error.message);
            }
        }

        async function testToolCall() {
            try {
                const result = await sendMcpRequest('tools/call', {
                    name: 'calculator_add',
                    arguments: {
                        a: 10,
                        b: 5
                    }
                });
                log('Tool Call 成功: ' + JSON.stringify(result));
            } catch (error) {
                log('Tool Call 失败: ' + error.message);
            }
        }

        async function calculate() {
            const num1 = parseFloat(document.getElementById('num1').value);
            const num2 = parseFloat(document.getElementById('num2').value);
            const operation = document.getElementById('operation').value;
            
            const toolName = 'calculator_' + operation;
            
            try {
                const result = await sendMcpRequest('tools/call', {
                    name: toolName,
                    arguments: {
                        a: num1,
                        b: num2
                    }
                });
                
                const resultDiv = document.getElementById('result');
                if (result.result && result.result.content && result.result.content[0]) {
                    resultDiv.innerHTML = `<div class="result">${result.result.content[0].text}</div>`;
                } else {
                    resultDiv.innerHTML = `<div class="result">计算结果: ${JSON.stringify(result)}</div>`;
                }
            } catch (error) {
                document.getElementById('result').innerHTML = `<div class="result" style="background: #f8d7da; color: #721c24;">计算错误: ${error.message}</div>`;
            }
        }

        // 页面加载时自动连接
        window.onload = function() {
            log('页面已加载，准备测试 Calculator MCP Server');
        };
    </script>
</body>
</html>