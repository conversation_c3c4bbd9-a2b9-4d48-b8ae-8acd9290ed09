# Calculator MCP Server - SSE 模式使用指南

## 概述

Calculator MCP Server 支持两种传输模式：
- **stdio模式**：通过标准输入输出与MCP客户端通信（默认模式）
- **SSE模式**：通过Server-Sent Events提供HTTP API接口

## SSE模式特性

### 优势
- 支持Web浏览器直接访问
- 提供RESTful API接口
- 实时事件推送
- 更好的调试和监控能力
- 支持跨域访问

### 端点说明
- `GET /mcp/sse` - SSE事件流端点
- `POST /mcp/message` - MCP协议消息处理
- `GET /mcp/info` - 服务器信息
- `GET /mcp/health` - 健康检查

## 快速开始

### 1. 使用启动器（推荐）

双击运行 `start-calculator-mcp.bat`，选择相应的模式：
- stdio模式：适用于Kiro IDE集成
- SSE模式：提供Web API接口

### 2. 命令行启动

```bash
# SSE模式
java -jar target/calcMcp-0.0.1-SNAPSHOT.jar --transport=sse

# stdio模式（默认）
java -jar target/calcMcp-0.0.1-SNAPSHOT.jar

# 使用环境变量
set MCP_TRANSPORT=sse
java -jar target/calcMcp-0.0.1-SNAPSHOT.jar

# 使用系统属性
java -Dmcp.transport=sse -jar target/calcMcp-0.0.1-SNAPSHOT.jar
```

### 3. 构建和测试

```bash
# 自动构建和测试
scripts/build-and-test.bat

# 手动构建
mvn clean package -DskipTests
```

## 配置说明

### MCP配置文件

在 `.kiro/settings/mcp.json` 中配置：

```json
{
  "mcpServers": {
    "calculator-sse": {
      "command": "java",
      "args": [
        "-jar",
        "target/calcMcp-0.0.1-SNAPSHOT.jar",
        "--transport=sse"
      ],
      "env": {
        "MCP_TRANSPORT": "sse"
      },
      "disabled": false,
      "autoApprove": [
        "calculator_add",
        "calculator_subtract",
        "calculator_multiply",
        "calculator_divide"
      ]
    }
  }
}
```

### 应用配置

在 `application.yml` 中的相关配置：

```yaml
server:
  port: 8080

mcp:
  server:
    transport: "sse"
    sse:
      endpoint: "/mcp/sse"
      message-endpoint: "/mcp/message"
      heartbeat-interval: 30s
```

## API 使用示例

### 1. 建立SSE连接

```javascript
const eventSource = new EventSource('http://localhost:8080/mcp/sse');

eventSource.onmessage = function(event) {
    console.log('收到消息:', event.data);
};

eventSource.addEventListener('ping', function(event) {
    console.log('心跳:', event.data);
});
```

### 2. 发送MCP请求

```javascript
async function sendMcpRequest(method, params = {}) {
    const request = {
        jsonrpc: "2.0",
        id: 1,
        method: method,
        params: params
    };

    const response = await fetch('http://localhost:8080/mcp/message', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(request)
    });

    return await response.json();
}
```

### 3. 初始化连接

```javascript
const initResult = await sendMcpRequest('initialize', {
    protocolVersion: "2024-11-05",
    capabilities: {},
    clientInfo: {
        name: "Test Client",
        version: "1.0.0"
    }
});
```

### 4. 获取工具列表

```javascript
const toolsResult = await sendMcpRequest('tools/list');
```

### 5. 调用计算器工具

```javascript
// 加法
const addResult = await sendMcpRequest('tools/call', {
    name: 'calculator_add',
    arguments: { a: 10, b: 5 }
});

// 减法
const subtractResult = await sendMcpRequest('tools/call', {
    name: 'calculator_subtract',
    arguments: { a: 10, b: 3 }
});

// 乘法
const multiplyResult = await sendMcpRequest('tools/call', {
    name: 'calculator_multiply',
    arguments: { a: 4, b: 6 }
});

// 除法
const divideResult = await sendMcpRequest('tools/call', {
    name: 'calculator_divide',
    arguments: { a: 20, b: 4 }
});
```

## 测试工具

### 1. HTML测试页面

打开 `test-sse-simple.html` 在浏览器中进行交互式测试。

### 2. Python测试脚本

运行 `test-mcp-sse.py` 进行自动化测试：

```bash
python test-mcp-sse.py
```

### 3. curl命令测试

```bash
# 健康检查
curl http://localhost:8080/mcp/health

# 服务器信息
curl http://localhost:8080/mcp/info

# MCP消息
curl -X POST http://localhost:8080/mcp/message \
  -H "Content-Type: application/json" \
  -d '{"jsonrpc":"2.0","id":1,"method":"tools/list"}'
```

## 故障排除

### 常见问题

1. **端口被占用**
   - 修改 `application.yml` 中的 `server.port`
   - 或使用 `--server.port=8081` 参数

2. **CORS错误**
   - 控制器已配置 `@CrossOrigin(origins = "*")`
   - 如需限制，修改origins配置

3. **SSE连接断开**
   - 检查网络连接
   - 查看服务器日志
   - 确认防火墙设置

4. **计算错误**
   - 检查输入参数格式
   - 注意除零错误
   - 查看详细错误信息

### 日志配置

开启调试日志：

```yaml
logging:
  level:
    io.kt666.mcp: DEBUG
    org.springframework.web: DEBUG
```

## 性能优化

### 连接池配置

```yaml
reactor:
  netty:
    pool:
      max-connections: 100
      max-idle-time: 30s
      max-life-time: 60s
```

### 并发限制

```yaml
mcp:
  server:
    max-concurrent-requests: 200
    request-timeout: 30s
```

## 安全考虑

1. **生产环境部署**
   - 配置适当的CORS策略
   - 使用HTTPS
   - 添加认证机制

2. **输入验证**
   - 已实现数字范围验证
   - 防止除零错误
   - 参数类型检查

## 扩展开发

### 添加新的计算工具

1. 在 `CalculatorService` 接口中添加方法
2. 在 `CalculatorServiceImpl` 中实现
3. 在 `McpSseController` 中添加工具定义
4. 更新测试用例

### 自定义响应格式

修改 `ResponseFormatter` 类来自定义响应格式。

## 版本信息

- MCP协议版本: 2024-11-05
- 服务器版本: 1.0.0
- Spring Boot版本: 3.x
- Java版本: 21+
#
# 项目结构

```
calcMcp/
├── src/main/java/io/kt666/mcp/calcmcp/
│   ├── CalcMcpApplication.java          # 主应用类，支持stdio和SSE模式
│   ├── controller/
│   │   ├── CalculatorController.java    # REST API控制器
│   │   ├── McpSseController.java        # SSE模式MCP协议处理
│   │   └── GlobalExceptionHandler.java # 全局异常处理
│   ├── service/
│   │   ├── CalculatorService.java       # 计算器服务接口
│   │   ├── InputValidator.java          # 输入验证接口
│   │   └── impl/
│   │       ├── CalculatorServiceImpl.java    # 计算器服务实现
│   │       └── InputValidatorImpl.java       # 输入验证实现
│   ├── model/
│   │   ├── CalculationResponse.java     # 计算响应模型
│   │   ├── CalculationRequest.java      # 计算请求模型
│   │   └── CalculationError.java        # 错误模型
│   ├── exception/
│   │   ├── CalculationException.java    # 计算异常基类
│   │   ├── DivisionByZeroException.java # 除零异常
│   │   └── InvalidInputException.java   # 无效输入异常
│   ├── util/
│   │   └── ResponseFormatter.java       # 响应格式化工具
│   └── mcp/
│       └── McpServer.java               # stdio模式MCP服务器
├── src/main/resources/
│   ├── application.yml                  # 应用配置
│   └── application.properties           # 简单配置
├── scripts/
│   ├── start-sse.bat                   # SSE模式启动脚本
│   ├── build-and-test.bat              # 构建和测试脚本
│   └── switch-mcp-mode.bat             # 模式切换脚本
├── .kiro/settings/
│   └── mcp.json                        # Kiro MCP配置
├── start-calculator-mcp.bat            # 主启动器
├── test-sse-simple.html                # Web测试页面
├── test-mcp-sse.py                     # Python自动化测试
└── README-SSE.md                       # SSE模式使用指南
```

## 支持的计算操作

### 基本运算
- **加法** (`calculator_add`): 支持2个或多个数字相加
- **减法** (`calculator_subtract`): 按顺序执行减法运算
- **乘法** (`calculator_multiply`): 支持2个或多个数字相乘
- **除法** (`calculator_divide`): 按顺序执行除法运算，自动检测除零错误

### 参数格式

支持两种参数格式：

1. **传统格式**（向后兼容）:
```json
{
  "name": "calculator_add",
  "arguments": {
    "a": 10,
    "b": 5
  }
}
```

2. **数组格式**（支持多个数字）:
```json
{
  "name": "calculator_add",
  "arguments": {
    "numbers": [1, 2, 3, 4, 5]
  }
}
```

### 计算示例

```javascript
// 两个数字相加
await sendMcpRequest('tools/call', {
    name: 'calculator_add',
    arguments: { a: 10, b: 5 }
});
// 结果: "10.00 + 5.00 = 15.00"

// 多个数字相加
await sendMcpRequest('tools/call', {
    name: 'calculator_add',
    arguments: { numbers: [1, 2, 3, 4] }
});
// 结果: "Added 4 numbers: 1.00, 2.00, 3.00, 4.00 = 10.00"

// 连续除法
await sendMcpRequest('tools/call', {
    name: 'calculator_divide',
    arguments: { numbers: [100, 2, 5] }
});
// 结果: "Divided 3 numbers: 100.00, 2.00, 5.00 = 10.00"
```

## 开发和调试

### 日志级别配置

```yaml
logging:
  level:
    io.kt666.mcp: DEBUG          # MCP相关日志
    org.springframework.web: INFO # Web相关日志
    reactor.netty: INFO          # Netty相关日志
```

### 开发模式启动

```bash
# 开发模式（详细日志）
java -jar target/calcMcp-0.0.1-SNAPSHOT.jar --spring.profiles.active=dev --transport=sse

# 生产模式
java -jar target/calcMcp-0.0.1-SNAPSHOT.jar --spring.profiles.active=prod --transport=sse
```

### 调试端点

- `GET /actuator/health` - 应用健康状态
- `GET /actuator/info` - 应用信息
- `GET /actuator/metrics` - 应用指标
- `GET /mcp/health` - MCP服务健康检查
- `GET /mcp/info` - MCP服务信息

## 部署建议

### 生产环境配置

1. **端口配置**: 修改 `application.yml` 中的 `server.port`
2. **CORS策略**: 根据需要限制 `@CrossOrigin` 的origins
3. **日志配置**: 使用适当的日志级别和文件输出
4. **性能调优**: 调整连接池和并发限制

### Docker部署（可选）

```dockerfile
FROM openjdk:21-jre-slim
COPY target/calcMcp-0.0.1-SNAPSHOT.jar app.jar
EXPOSE 8080
ENTRYPOINT ["java", "-jar", "/app.jar", "--transport=sse"]
```

### 监控和维护

- 定期检查日志文件
- 监控内存和CPU使用情况
- 使用健康检查端点进行服务监控
- 配置适当的重启策略

## 常见问题解答

### Q: 如何在Kiro中使用SSE模式？
A: 编辑 `.kiro/settings/mcp.json`，启用 `calculator-sse` 服务器，设置 `disabled: false`。

### Q: SSE模式和stdio模式有什么区别？
A: stdio模式通过标准输入输出通信，适合IDE集成；SSE模式提供HTTP API，支持Web访问和调试。

### Q: 如何添加新的计算功能？
A: 1) 在 `CalculatorService` 接口中添加方法；2) 在实现类中实现逻辑；3) 在控制器中添加工具定义。

### Q: 支持哪些数字格式？
A: 支持整数和小数，自动处理精度问题，检测NaN和无穷大值。

### Q: 如何处理并发请求？
A: SSE模式基于Spring WebFlux，天然支持高并发，可通过配置调整连接池大小。

## 更新日志

### v1.0.0
- ✅ 支持stdio和SSE两种传输模式
- ✅ 实现基本四则运算
- ✅ 支持多数字计算
- ✅ 完整的错误处理和输入验证
- ✅ Web测试界面和自动化测试
- ✅ 详细的文档和使用指南