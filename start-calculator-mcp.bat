@echo off
title Calculator MCP Server 启动器

:menu
cls
echo ========================================
echo    Calculator MCP Server 启动器
echo ========================================
echo.
echo 请选择启动模式:
echo.
echo 1. stdio模式 (标准输入输出)
echo    - 适用于Kiro IDE集成
echo    - 通过stdin/stdout通信
echo.
echo 2. SSE模式 (Server-Sent Events)
echo    - 提供Web API接口
echo    - 支持浏览器直接访问
echo    - 端口: 8080
echo.
echo 3. 构建项目
echo 4. 运行测试
echo 5. 切换MCP配置
echo 6. 退出
echo.

set /p choice="请输入选择 (1-6): "

if "%choice%"=="1" goto start_stdio
if "%choice%"=="2" goto start_sse
if "%choice%"=="3" goto build_project
if "%choice%"=="4" goto run_tests
if "%choice%"=="5" goto switch_config
if "%choice%"=="6" goto end

echo 无效选择，请重新选择
pause
goto menu

:start_stdio
cls
echo ========================================
echo 启动 Calculator MCP Server - stdio模式
echo ========================================
echo.
echo 💡 提示: 
echo   - 此模式适用于Kiro IDE集成
echo   - 按 Ctrl+C 停止服务器
echo   - 服务器将通过标准输入输出与客户端通信
echo.
echo 🚀 正在启动...
echo.

java -jar target/calcMcp-0.0.1-SNAPSHOT.jar
echo.
echo 服务器已停止
pause
goto menu

:start_sse
cls
echo ========================================
echo 启动 Calculator MCP Server - SSE模式
echo ========================================
echo.
echo 💡 提示:
echo   - 服务器将在端口8080启动
echo   - 按 Ctrl+C 停止服务器
echo   - 可通过浏览器访问 http://localhost:8080/mcp/health
echo.
echo 🌐 可用端点:
echo   - 健康检查: http://localhost:8080/mcp/health
echo   - 服务信息: http://localhost:8080/mcp/info
echo   - SSE连接:  http://localhost:8080/mcp/sse
echo   - 消息处理: http://localhost:8080/mcp/message
echo.
echo 🚀 正在启动...
echo.

java -jar target/calcMcp-0.0.1-SNAPSHOT.jar --transport=sse
echo.
echo 服务器已停止
pause
goto menu

:build_project
cls
echo ========================================
echo 构建项目
echo ========================================
echo.

if not exist "pom.xml" (
    echo ❌ 未找到pom.xml文件
    echo 请确保在项目根目录运行此脚本
    pause
    goto menu
)

echo 🔨 正在构建项目...
call mvn clean package -DskipTests

if %errorlevel% equ 0 (
    echo ✅ 构建成功
    if exist "target\calcMcp-0.0.1-SNAPSHOT.jar" (
        echo ✅ JAR文件已生成: target\calcMcp-0.0.1-SNAPSHOT.jar
    )
) else (
    echo ❌ 构建失败
)

pause
goto menu

:run_tests
cls
echo ========================================
echo 运行测试
echo ========================================
echo.
echo 请选择测试类型:
echo.
echo 1. Maven单元测试
echo 2. SSE模式集成测试 (需要Python)
echo 3. 打开Web测试页面
echo 4. 返回主菜单
echo.

set /p test_choice="请输入选择 (1-4): "

if "%test_choice%"=="1" goto maven_test
if "%test_choice%"=="2" goto python_test
if "%test_choice%"=="3" goto web_test
if "%test_choice%"=="4" goto menu

echo 无效选择
pause
goto run_tests

:maven_test
echo.
echo 🧪 运行Maven单元测试...
call mvn test
pause
goto run_tests

:python_test
echo.
echo 🐍 运行Python集成测试...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python未安装
    echo 请安装Python并确保pip install sseclient-py aiohttp requests
    pause
    goto run_tests
)

echo 请确保SSE模式服务器正在运行 (端口8080)
echo 按任意键继续，或Ctrl+C取消...
pause >nul

python test-mcp-sse.py --extended
pause
goto run_tests

:web_test
echo.
echo 🌐 打开Web测试页面...
if exist "test-sse-simple.html" (
    start test-sse-simple.html
    echo ✅ 已在默认浏览器中打开测试页面
    echo 💡 请确保SSE模式服务器正在运行 (端口8080)
) else (
    echo ❌ 测试页面文件不存在: test-sse-simple.html
)
pause
goto run_tests

:switch_config
cls
echo ========================================
echo 切换MCP配置
echo ========================================
echo.

if exist "scripts\switch-mcp-mode.bat" (
    call scripts\switch-mcp-mode.bat
) else (
    echo ❌ 配置切换脚本不存在: scripts\switch-mcp-mode.bat
    pause
)
goto menu

:end
echo.
echo 👋 感谢使用 Calculator MCP Server!
echo.
pause