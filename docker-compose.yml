version: '3.8'

services:
  calculator-mcp-server:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: calculator-mcp-server
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - SERVER_PORT=8080
      - JAVA_OPTS=-Xms256m -Xmx512m -XX:+UseG1GC
    volumes:
      - ./logs:/app/logs
      - ./config:/app/config
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/mcp/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    networks:
      - mcp-network

  # 可选：添加监控服务
  prometheus:
    image: prom/prometheus:latest
    container_name: calculator-mcp-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    networks:
      - mcp-network
    profiles:
      - monitoring

  grafana:
    image: grafana/grafana:latest
    container_name: calculator-mcp-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana-storage:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - mcp-network
    profiles:
      - monitoring

networks:
  mcp-network:
    driver: bridge

volumes:
  grafana-storage: