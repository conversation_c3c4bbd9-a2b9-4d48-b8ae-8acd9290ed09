#!/bin/bash

# 测试SSE MCP服务器的脚本

echo "=== Calculator MCP Server SSE Test ==="
echo

# 检查服务器是否运行
echo "1. 检查服务器健康状态..."
curl -s http://localhost:8080/mcp/health | jq .
echo

# 获取服务器信息
echo "2. 获取服务器信息..."
curl -s http://localhost:8080/mcp/info | jq .
echo

# 测试初始化
echo "3. 测试MCP初始化..."
curl -s -X POST http://localhost:8080/mcp/message \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 1,
    "method": "initialize",
    "params": {
      "protocolVersion": "2024-11-05",
      "capabilities": {},
      "clientInfo": {
        "name": "test-client",
        "version": "1.0.0"
      }
    }
  }' | jq .
echo

# 测试工具列表
echo "4. 获取工具列表..."
curl -s -X POST http://localhost:8080/mcp/message \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 2,
    "method": "tools/list",
    "params": {}
  }' | jq .
echo

# 测试加法
echo "5. 测试加法计算 (10 + 5)..."
curl -s -X POST http://localhost:8080/mcp/message \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 3,
    "method": "tools/call",
    "params": {
      "name": "calculator_add",
      "arguments": {
        "a": 10.0,
        "b": 5.0
      }
    }
  }' | jq .
echo

# 测试减法
echo "6. 测试减法计算 (20 - 8)..."
curl -s -X POST http://localhost:8080/mcp/message \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 4,
    "method": "tools/call",
    "params": {
      "name": "calculator_subtract",
      "arguments": {
        "a": 20.0,
        "b": 8.0
      }
    }
  }' | jq .
echo

# 测试乘法
echo "7. 测试乘法计算 (6 × 7)..."
curl -s -X POST http://localhost:8080/mcp/message \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 5,
    "method": "tools/call",
    "params": {
      "name": "calculator_multiply",
      "arguments": {
        "a": 6.0,
        "b": 7.0
      }
    }
  }' | jq .
echo

# 测试除法
echo "8. 测试除法计算 (100 ÷ 4)..."
curl -s -X POST http://localhost:8080/mcp/message \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 6,
    "method": "tools/call",
    "params": {
      "name": "calculator_divide",
      "arguments": {
        "a": 100.0,
        "b": 4.0
      }
    }
  }' | jq .
echo

# 测试除零错误
echo "9. 测试除零错误处理 (10 ÷ 0)..."
curl -s -X POST http://localhost:8080/mcp/message \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 7,
    "method": "tools/call",
    "params": {
      "name": "calculator_divide",
      "arguments": {
        "a": 10.0,
        "b": 0.0
      }
    }
  }' | jq .
echo

echo "=== 测试完成 ==="