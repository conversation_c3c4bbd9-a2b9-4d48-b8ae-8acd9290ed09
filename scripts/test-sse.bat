@echo off
echo === Calculator MCP Server SSE Test ===
echo.

echo 1. 检查服务器健康状态...
curl -s http://localhost:8080/mcp/health
echo.
echo.

echo 2. 获取服务器信息...
curl -s http://localhost:8080/mcp/info
echo.
echo.

echo 3. 测试MCP初始化...
curl -s -X POST http://localhost:8080/mcp/message -H "Content-Type: application/json" -d "{\"jsonrpc\": \"2.0\", \"id\": 1, \"method\": \"initialize\", \"params\": {\"protocolVersion\": \"2024-11-05\", \"capabilities\": {}, \"clientInfo\": {\"name\": \"test-client\", \"version\": \"1.0.0\"}}}"
echo.
echo.

echo 4. 获取工具列表...
curl -s -X POST http://localhost:8080/mcp/message -H "Content-Type: application/json" -d "{\"jsonrpc\": \"2.0\", \"id\": 2, \"method\": \"tools/list\", \"params\": {}}"
echo.
echo.

echo 5. 测试加法计算 (10 + 5)...
curl -s -X POST http://localhost:8080/mcp/message -H "Content-Type: application/json" -d "{\"jsonrpc\": \"2.0\", \"id\": 3, \"method\": \"tools/call\", \"params\": {\"name\": \"calculator_add\", \"arguments\": {\"a\": 10.0, \"b\": 5.0}}}"
echo.
echo.

echo 6. 测试减法计算 (20 - 8)...
curl -s -X POST http://localhost:8080/mcp/message -H "Content-Type: application/json" -d "{\"jsonrpc\": \"2.0\", \"id\": 4, \"method\": \"tools/call\", \"params\": {\"name\": \"calculator_subtract\", \"arguments\": {\"a\": 20.0, \"b\": 8.0}}}"
echo.
echo.

echo 7. 测试乘法计算 (6 × 7)...
curl -s -X POST http://localhost:8080/mcp/message -H "Content-Type: application/json" -d "{\"jsonrpc\": \"2.0\", \"id\": 5, \"method\": \"tools/call\", \"params\": {\"name\": \"calculator_multiply\", \"arguments\": {\"a\": 6.0, \"b\": 7.0}}}"
echo.
echo.

echo 8. 测试除法计算 (100 ÷ 4)...
curl -s -X POST http://localhost:8080/mcp/message -H "Content-Type: application/json" -d "{\"jsonrpc\": \"2.0\", \"id\": 6, \"method\": \"tools/call\", \"params\": {\"name\": \"calculator_divide\", \"arguments\": {\"a\": 100.0, \"b\": 4.0}}}"
echo.
echo.

echo 9. 测试除零错误处理 (10 ÷ 0)...
curl -s -X POST http://localhost:8080/mcp/message -H "Content-Type: application/json" -d "{\"jsonrpc\": \"2.0\", \"id\": 7, \"method\": \"tools/call\", \"params\": {\"name\": \"calculator_divide\", \"arguments\": {\"a\": 10.0, \"b\": 0.0}}}"
echo.
echo.

echo === 测试完成 ===
pause