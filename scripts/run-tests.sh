#!/bin/bash

# Calculator MCP Server 测试运行脚本

# 设置脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 默认参数
TEST_TYPE="all"
GENERATE_REPORT=true
SKIP_COMPILE=false

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --type)
            TEST_TYPE="$2"
            shift 2
            ;;
        --no-report)
            GENERATE_REPORT=false
            shift
            ;;
        --skip-compile)
            SKIP_COMPILE=true
            shift
            ;;
        --help)
            echo "Usage: $0 [OPTIONS]"
            echo "Options:"
            echo "  --type TYPE          Test type: all, unit, integration, performance, e2e"
            echo "  --no-report          Skip generating test report"
            echo "  --skip-compile       Skip compilation step"
            echo "  --help               Show this help message"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

echo -e "${BLUE}==================================================================${NC}"
echo -e "${BLUE}Calculator MCP Server - Test Runner${NC}"
echo -e "${BLUE}==================================================================${NC}"
echo -e "Test Type: ${YELLOW}$TEST_TYPE${NC}"
echo -e "Generate Report: ${YELLOW}$GENERATE_REPORT${NC}"
echo -e "Skip Compile: ${YELLOW}$SKIP_COMPILE${NC}"
echo ""

# 切换到项目目录
cd "$PROJECT_DIR"

# 编译项目
if [ "$SKIP_COMPILE" = false ]; then
    echo -e "${BLUE}Step 1: Compiling project...${NC}"
    if ! ./mvnw clean compile test-compile; then
        echo -e "${RED}❌ Compilation failed${NC}"
        exit 1
    fi
    echo -e "${GREEN}✅ Compilation successful${NC}"
    echo ""
fi

# 创建测试报告目录
mkdir -p target/test-reports

# 运行测试
echo -e "${BLUE}Step 2: Running tests...${NC}"

case $TEST_TYPE in
    "unit")
        echo -e "${YELLOW}Running unit tests...${NC}"
        TEST_COMMAND="./mvnw test -Dtest=*ServiceImplTest,*ValidatorImplTest"
        ;;
    "integration")
        echo -e "${YELLOW}Running integration tests...${NC}"
        TEST_COMMAND="./mvnw test -Dtest=McpProtocolIntegrationTest"
        ;;
    "performance")
        echo -e "${YELLOW}Running performance tests...${NC}"
        TEST_COMMAND="./mvnw test -Dtest=ConcurrencyAndPerformanceTest"
        ;;
    "e2e")
        echo -e "${YELLOW}Running end-to-end tests...${NC}"
        TEST_COMMAND="./mvnw test -Dtest=EndToEndTestSuite"
        ;;
    "all")
        echo -e "${YELLOW}Running all tests...${NC}"
        TEST_COMMAND="./mvnw test"
        ;;
    *)
        echo -e "${RED}❌ Unknown test type: $TEST_TYPE${NC}"
        exit 1
        ;;
esac

# 执行测试命令
if eval $TEST_COMMAND; then
    echo -e "${GREEN}✅ Tests completed successfully${NC}"
    TEST_SUCCESS=true
else
    echo -e "${RED}❌ Some tests failed${NC}"
    TEST_SUCCESS=false
fi

echo ""

# 生成测试报告
if [ "$GENERATE_REPORT" = true ]; then
    echo -e "${BLUE}Step 3: Generating test report...${NC}"
    
    # 生成Surefire报告
    if ./mvnw surefire-report:report-only; then
        echo -e "${GREEN}✅ Test report generated${NC}"
        echo -e "Report location: ${YELLOW}target/site/surefire-report.html${NC}"
    else
        echo -e "${YELLOW}⚠️  Test report generation failed${NC}"
    fi
    
    # 复制报告到易于访问的位置
    if [ -f "target/site/surefire-report.html" ]; then
        cp target/site/surefire-report.html target/test-reports/
        echo -e "Report copied to: ${YELLOW}target/test-reports/surefire-report.html${NC}"
    fi
fi

# 显示测试结果摘要
echo ""
echo -e "${BLUE}==================================================================${NC}"
echo -e "${BLUE}Test Results Summary${NC}"
echo -e "${BLUE}==================================================================${NC}"

if [ -f "target/surefire-reports/TEST-*.xml" ]; then
    # 解析测试结果
    TOTAL_TESTS=$(grep -h "tests=" target/surefire-reports/TEST-*.xml | sed 's/.*tests="\([0-9]*\)".*/\1/' | awk '{sum+=$1} END {print sum}')
    FAILED_TESTS=$(grep -h "failures=" target/surefire-reports/TEST-*.xml | sed 's/.*failures="\([0-9]*\)".*/\1/' | awk '{sum+=$1} END {print sum}')
    ERROR_TESTS=$(grep -h "errors=" target/surefire-reports/TEST-*.xml | sed 's/.*errors="\([0-9]*\)".*/\1/' | awk '{sum+=$1} END {print sum}')
    SKIPPED_TESTS=$(grep -h "skipped=" target/surefire-reports/TEST-*.xml | sed 's/.*skipped="\([0-9]*\)".*/\1/' | awk '{sum+=$1} END {print sum}')
    
    PASSED_TESTS=$((TOTAL_TESTS - FAILED_TESTS - ERROR_TESTS - SKIPPED_TESTS))
    
    echo -e "Total Tests: ${BLUE}$TOTAL_TESTS${NC}"
    echo -e "Passed: ${GREEN}$PASSED_TESTS${NC}"
    echo -e "Failed: ${RED}$FAILED_TESTS${NC}"
    echo -e "Errors: ${RED}$ERROR_TESTS${NC}"
    echo -e "Skipped: ${YELLOW}$SKIPPED_TESTS${NC}"
else
    echo -e "${YELLOW}No test results found${NC}"
fi

echo ""

# 显示可用的报告和日志
echo -e "${BLUE}Available Reports and Logs:${NC}"
echo -e "• Test Reports: ${YELLOW}target/surefire-reports/${NC}"
echo -e "• HTML Report: ${YELLOW}target/test-reports/surefire-report.html${NC}"
echo -e "• Application Logs: ${YELLOW}logs/${NC}"

echo ""
echo -e "${BLUE}==================================================================${NC}"

# 退出状态
if [ "$TEST_SUCCESS" = true ]; then
    echo -e "${GREEN}🎉 All tests completed successfully!${NC}"
    exit 0
else
    echo -e "${RED}❌ Some tests failed. Check the reports for details.${NC}"
    exit 1
fi