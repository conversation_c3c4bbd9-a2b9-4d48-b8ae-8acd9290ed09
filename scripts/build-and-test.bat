@echo off
echo ========================================
echo Calculator MCP Server 构建和测试脚本
echo ========================================

REM 检查Java环境
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Java未安装或未配置到PATH
    echo 请安装Java 21或更高版本
    pause
    exit /b 1
)

echo ✅ Java环境检查通过

REM 检查Maven环境
mvn -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Maven未安装或未配置到PATH
    echo 请安装Maven 3.6或更高版本
    pause
    exit /b 1
)

echo ✅ Maven环境检查通过

REM 清理和编译
echo.
echo 🔨 开始构建项目...
call mvn clean compile
if %errorlevel% neq 0 (
    echo ❌ 编译失败
    pause
    exit /b 1
)

echo ✅ 编译成功

REM 运行测试
echo.
echo 🧪 运行单元测试...
call mvn test
if %errorlevel% neq 0 (
    echo ⚠️  部分测试失败，但继续构建
)

REM 打包
echo.
echo 📦 打包应用...
call mvn package -DskipTests
if %errorlevel% neq 0 (
    echo ❌ 打包失败
    pause
    exit /b 1
)

echo ✅ 打包成功

REM 检查JAR文件
if not exist "target\calcMcp-0.0.1-SNAPSHOT.jar" (
    echo ❌ JAR文件未生成
    pause
    exit /b 1
)

echo ✅ JAR文件生成成功

REM 测试stdio模式
echo.
echo 🔍 测试stdio模式启动...
timeout /t 2 >nul
start /b java -jar target\calcMcp-0.0.1-SNAPSHOT.jar --stdio > stdio-test.log 2>&1
timeout /t 3 >nul

REM 检查stdio模式日志
if exist "stdio-test.log" (
    findstr /c:"Calculator MCP Server started with stdio transport" stdio-test.log >nul
    if %errorlevel% equ 0 (
        echo ✅ stdio模式启动成功
    ) else (
        echo ⚠️  stdio模式启动可能有问题，请检查日志
    )
) else (
    echo ⚠️  stdio模式日志文件未生成
)

REM 停止stdio模式测试
taskkill /f /im java.exe >nul 2>&1

REM 测试SSE模式
echo.
echo 🌐 测试SSE模式启动...
start /b java -jar target\calcMcp-0.0.1-SNAPSHOT.jar --transport=sse > sse-test.log 2>&1
timeout /t 5 >nul

REM 检查SSE模式
curl -s http://localhost:8080/mcp/health >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ SSE模式启动成功，服务可访问
    
    REM 运行Python测试（如果可用）
    python --version >nul 2>&1
    if %errorlevel% equ 0 (
        echo.
        echo 🐍 运行Python自动化测试...
        python test-mcp-sse.py
    ) else (
        echo ⚠️  Python未安装，跳过自动化测试
        echo 💡 你可以手动打开 test-sse-simple.html 进行测试
    )
) else (
    echo ❌ SSE模式启动失败或服务不可访问
    echo 请检查端口8080是否被占用
)

REM 停止SSE模式测试
taskkill /f /im java.exe >nul 2>&1

echo.
echo ========================================
echo 构建和测试完成
echo ========================================
echo.
echo 📁 生成的文件:
echo   - target\calcMcp-0.0.1-SNAPSHOT.jar
echo   - stdio-test.log
echo   - sse-test.log
echo.
echo 🚀 启动命令:
echo   stdio模式: java -jar target\calcMcp-0.0.1-SNAPSHOT.jar
echo   SSE模式:   java -jar target\calcMcp-0.0.1-SNAPSHOT.jar --transport=sse
echo.
echo 🌐 SSE模式端点:
echo   健康检查: http://localhost:8080/mcp/health
echo   服务信息: http://localhost:8080/mcp/info
echo   SSE连接:  http://localhost:8080/mcp/sse
echo   测试页面: test-sse-simple.html
echo.

pause