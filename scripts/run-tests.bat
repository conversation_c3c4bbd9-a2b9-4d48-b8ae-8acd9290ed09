@echo off
REM Calculator MCP Server 测试运行脚本 (Windows)

setlocal enabledelayedexpansion

REM 设置脚本目录
set SCRIPT_DIR=%~dp0
set PROJECT_DIR=%SCRIPT_DIR%..

REM 设置默认值
set TEST_TYPE=all
set GENERATE_REPORT=true
set SKIP_COMPILE=false

REM 解析命令行参数
:parse_args
if "%~1"=="" goto end_parse
if "%~1"=="--type" (
    set TEST_TYPE=%~2
    shift
    shift
    goto parse_args
)
if "%~1"=="--no-report" (
    set GENERATE_REPORT=false
    shift
    goto parse_args
)
if "%~1"=="--skip-compile" (
    set SKIP_COMPILE=true
    shift
    goto parse_args
)
if "%~1"=="--help" (
    echo Usage: %0 [OPTIONS]
    echo Options:
    echo   --type TYPE          Test type: all, unit, integration, performance, e2e
    echo   --no-report          Skip generating test report
    echo   --skip-compile       Skip compilation step
    echo   --help               Show this help message
    exit /b 0
)
echo Unknown option: %~1
echo Use --help for usage information
exit /b 1

:end_parse

echo ==================================================================
echo Calculator MCP Server - Test Runner
echo ==================================================================
echo Test Type: %TEST_TYPE%
echo Generate Report: %GENERATE_REPORT%
echo Skip Compile: %SKIP_COMPILE%
echo.

REM 切换到项目目录
cd /d "%PROJECT_DIR%"

REM 编译项目
if "%SKIP_COMPILE%"=="false" (
    echo Step 1: Compiling project...
    call mvnw.cmd clean compile test-compile
    if errorlevel 1 (
        echo ❌ Compilation failed
        exit /b 1
    )
    echo ✅ Compilation successful
    echo.
)

REM 创建测试报告目录
if not exist "target\test-reports" mkdir "target\test-reports"

REM 运行测试
echo Step 2: Running tests...

if "%TEST_TYPE%"=="unit" (
    echo Running unit tests...
    set TEST_COMMAND=mvnw.cmd test -Dtest=*ServiceImplTest,*ValidatorImplTest
) else if "%TEST_TYPE%"=="integration" (
    echo Running integration tests...
    set TEST_COMMAND=mvnw.cmd test -Dtest=McpProtocolIntegrationTest
) else if "%TEST_TYPE%"=="performance" (
    echo Running performance tests...
    set TEST_COMMAND=mvnw.cmd test -Dtest=ConcurrencyAndPerformanceTest
) else if "%TEST_TYPE%"=="e2e" (
    echo Running end-to-end tests...
    set TEST_COMMAND=mvnw.cmd test -Dtest=EndToEndTestSuite
) else if "%TEST_TYPE%"=="all" (
    echo Running all tests...
    set TEST_COMMAND=mvnw.cmd test
) else (
    echo ❌ Unknown test type: %TEST_TYPE%
    exit /b 1
)

REM 执行测试命令
call %TEST_COMMAND%
if errorlevel 1 (
    echo ❌ Some tests failed
    set TEST_SUCCESS=false
) else (
    echo ✅ Tests completed successfully
    set TEST_SUCCESS=true
)

echo.

REM 生成测试报告
if "%GENERATE_REPORT%"=="true" (
    echo Step 3: Generating test report...
    
    call mvnw.cmd surefire-report:report-only
    if errorlevel 1 (
        echo ⚠️  Test report generation failed
    ) else (
        echo ✅ Test report generated
        echo Report location: target\site\surefire-report.html
    )
    
    REM 复制报告到易于访问的位置
    if exist "target\site\surefire-report.html" (
        copy "target\site\surefire-report.html" "target\test-reports\"
        echo Report copied to: target\test-reports\surefire-report.html
    )
)

REM 显示测试结果摘要
echo.
echo ==================================================================
echo Test Results Summary
echo ==================================================================

if exist "target\surefire-reports\TEST-*.xml" (
    echo Test results available in: target\surefire-reports\
) else (
    echo No test results found
)

echo.

REM 显示可用的报告和日志
echo Available Reports and Logs:
echo • Test Reports: target\surefire-reports\
echo • HTML Report: target\test-reports\surefire-report.html
echo • Application Logs: logs\

echo.
echo ==================================================================

REM 退出状态
if "%TEST_SUCCESS%"=="true" (
    echo 🎉 All tests completed successfully!
    exit /b 0
) else (
    echo ❌ Some tests failed. Check the reports for details.
    exit /b 1
)