@echo off
echo Building Calculator MCP Server...

REM 清理并构建项目
call mvn clean package -DskipTests

if %ERRORLEVEL% neq 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo Build successful!
echo.

REM 询问用户选择运行模式
echo Choose transport mode:
echo 1. STDIO (default)
echo 2. SSE
echo.
set /p choice="Enter your choice (1 or 2): "

if "%choice%"=="2" (
    echo Starting Calculator MCP Server in SSE mode...
    echo Server will be available at: http://localhost:8080
    echo SSE endpoint: http://localhost:8080/mcp/sse
    echo Message endpoint: http://localhost:8080/mcp/message
    echo Health check: http://localhost:8080/mcp/health
    echo.
    java -jar target/calcMcp-0.0.1-SNAPSHOT.jar --transport=sse
) else (
    echo Starting Calculator MCP Server in STDIO mode...
    java -jar target/calcMcp-0.0.1-SNAPSHOT.jar --transport=stdio
)

pause