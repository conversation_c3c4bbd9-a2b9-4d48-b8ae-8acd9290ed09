@echo off
setlocal enabledelayedexpansion

echo ========================================
echo Calculator MCP Server 模式切换工具
echo ========================================

REM 检查配置文件是否存在
if not exist ".kiro\settings\mcp.json" (
    echo ❌ MCP配置文件不存在: .kiro\settings\mcp.json
    echo 请先创建配置文件
    pause
    exit /b 1
)

echo 当前MCP配置:
echo.

REM 显示当前配置状态
findstr /c:"calculator" .kiro\settings\mcp.json
findstr /c:"calculator-sse" .kiro\settings\mcp.json

echo.
echo 请选择要启用的模式:
echo 1. stdio模式 (标准输入输出，默认)
echo 2. SSE模式 (Server-Sent Events，Web API)
echo 3. 查看当前配置
echo 4. 退出
echo.

set /p choice="请输入选择 (1-4): "

if "%choice%"=="1" goto enable_stdio
if "%choice%"=="2" goto enable_sse
if "%choice%"=="3" goto show_config
if "%choice%"=="4" goto end
echo 无效选择，请重新运行脚本
pause
exit /b 1

:enable_stdio
echo.
echo 🔄 切换到stdio模式...

REM 备份原配置
copy ".kiro\settings\mcp.json" ".kiro\settings\mcp.json.backup" >nul

REM 使用PowerShell修改JSON配置
powershell -Command "& {
    $json = Get-Content '.kiro\settings\mcp.json' | ConvertFrom-Json;
    $json.mcpServers.calculator.disabled = $false;
    $json.mcpServers.'calculator-sse'.disabled = $true;
    $json | ConvertTo-Json -Depth 10 | Set-Content '.kiro\settings\mcp.json'
}"

if %errorlevel% equ 0 (
    echo ✅ 已切换到stdio模式
    echo 💡 重启Kiro以应用配置更改
) else (
    echo ❌ 配置更新失败
    copy ".kiro\settings\mcp.json.backup" ".kiro\settings\mcp.json" >nul
)
goto end

:enable_sse
echo.
echo 🔄 切换到SSE模式...

REM 备份原配置
copy ".kiro\settings\mcp.json" ".kiro\settings\mcp.json.backup" >nul

REM 使用PowerShell修改JSON配置
powershell -Command "& {
    $json = Get-Content '.kiro\settings\mcp.json' | ConvertFrom-Json;
    $json.mcpServers.calculator.disabled = $true;
    $json.mcpServers.'calculator-sse'.disabled = $false;
    $json | ConvertTo-Json -Depth 10 | Set-Content '.kiro\settings\mcp.json'
}"

if %errorlevel% equ 0 (
    echo ✅ 已切换到SSE模式
    echo 💡 重启Kiro以应用配置更改
    echo.
    echo 🌐 SSE模式端点:
    echo   - 健康检查: http://localhost:8080/mcp/health
    echo   - 服务信息: http://localhost:8080/mcp/info
    echo   - SSE连接:  http://localhost:8080/mcp/sse
    echo   - 测试页面: test-sse-simple.html
) else (
    echo ❌ 配置更新失败
    copy ".kiro\settings\mcp.json.backup" ".kiro\settings\mcp.json" >nul
)
goto end

:show_config
echo.
echo 📋 当前MCP配置:
echo ========================================
type ".kiro\settings\mcp.json"
echo ========================================
pause
goto end

:end
echo.
echo 操作完成
pause