#!/bin/bash

# Calculator MCP Server 启动脚本

# 设置脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# 设置Java选项
JAVA_OPTS="${JAVA_OPTS} -Xms256m -Xmx512m"
JAVA_OPTS="${JAVA_OPTS} -XX:+UseG1GC"
JAVA_OPTS="${JAVA_OPTS} -XX:MaxGCPauseMillis=200"
JAVA_OPTS="${JAVA_OPTS} -Dfile.encoding=UTF-8"
JAVA_OPTS="${JAVA_OPTS} -Duser.timezone=Asia/Shanghai"

# 设置应用选项
APP_OPTS=""
PROFILE="prod"
PORT="8080"

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --profile)
            PROFILE="$2"
            shift 2
            ;;
        --port)
            PORT="$2"
            shift 2
            ;;
        --debug)
            JAVA_OPTS="${JAVA_OPTS} -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=5005"
            shift
            ;;
        --help)
            echo "Usage: $0 [OPTIONS]"
            echo "Options:"
            echo "  --profile PROFILE    Set active profile (dev, test, prod). Default: prod"
            echo "  --port PORT          Set server port. Default: 8080"
            echo "  --debug              Enable debug mode on port 5005"
            echo "  --help               Show this help message"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# 设置Spring配置
APP_OPTS="${APP_OPTS} --spring.profiles.active=${PROFILE}"
APP_OPTS="${APP_OPTS} --server.port=${PORT}"

# 检查Java版本
if ! command -v java &> /dev/null; then
    echo "Error: Java is not installed or not in PATH"
    exit 1
fi

JAVA_VERSION=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2 | cut -d'.' -f1)
if [[ "$JAVA_VERSION" -lt 21 ]]; then
    echo "Error: Java 21 or higher is required. Current version: $JAVA_VERSION"
    exit 1
fi

# 创建日志目录
mkdir -p "$PROJECT_DIR/logs"

# 设置JAR文件路径
JAR_FILE="$PROJECT_DIR/target/calcMcp-0.0.1-SNAPSHOT.jar"

# 检查JAR文件是否存在
if [[ ! -f "$JAR_FILE" ]]; then
    echo "Error: JAR file not found at $JAR_FILE"
    echo "Please run 'mvn clean package' first"
    exit 1
fi

echo "Starting Calculator MCP Server..."
echo "Profile: $PROFILE"
echo "Port: $PORT"
echo "Java Options: $JAVA_OPTS"
echo "App Options: $APP_OPTS"
echo ""

# 启动应用
exec java $JAVA_OPTS -jar "$JAR_FILE" $APP_OPTS