@echo off
REM Calculator MCP Server 启动脚本 (Windows)

setlocal enabledelayedexpansion

REM 设置脚本目录
set SCRIPT_DIR=%~dp0
set PROJECT_DIR=%SCRIPT_DIR%..

REM 设置默认值
set PROFILE=prod
set PORT=8080
set DEBUG_MODE=false

REM 设置Java选项
set JAVA_OPTS=-Xms256m -Xmx512m
set JAVA_OPTS=%JAVA_OPTS% -XX:+UseG1GC
set JAVA_OPTS=%JAVA_OPTS% -XX:MaxGCPauseMillis=200
set JAVA_OPTS=%JAVA_OPTS% -Dfile.encoding=UTF-8
set JAVA_OPTS=%JAVA_OPTS% -Duser.timezone=Asia/Shanghai
set JAVA_OPTS=%JAVA_OPTS% -Dspring.devtools.restart.enabled=false

REM 解析命令行参数
:parse_args
if "%~1"=="" goto end_parse
if "%~1"=="--profile" (
    set PROFILE=%~2
    shift
    shift
    goto parse_args
)
if "%~1"=="--port" (
    set PORT=%~2
    shift
    shift
    goto parse_args
)
if "%~1"=="--debug" (
    set DEBUG_MODE=true
    set JAVA_OPTS=%JAVA_OPTS% -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=5005
    shift
    goto parse_args
)
if "%~1"=="--help" (
    echo Usage: %0 [OPTIONS]
    echo Options:
    echo   --profile PROFILE    Set active profile (dev, test, prod). Default: prod
    echo   --port PORT          Set server port. Default: 8080
    echo   --debug              Enable debug mode on port 5005
    echo   --help               Show this help message
    exit /b 0
)
echo Unknown option: %~1
echo Use --help for usage information
exit /b 1

:end_parse

REM 设置应用选项
set APP_OPTS=--spring.profiles.active=%PROFILE%
set APP_OPTS=%APP_OPTS% --server.port=%PORT%

REM 检查Java是否安装
java -version >nul 2>&1
if errorlevel 1 (
    echo Error: Java is not installed or not in PATH
    exit /b 1
)

REM 创建日志目录
if not exist "%PROJECT_DIR%\logs" mkdir "%PROJECT_DIR%\logs"

REM 设置JAR文件路径
set JAR_FILE=%PROJECT_DIR%\target\calcMcp-0.0.1-SNAPSHOT.jar

REM 检查JAR文件是否存在
if not exist "%JAR_FILE%" (
    echo Error: JAR file not found at %JAR_FILE%
    echo Please run 'mvn clean package' first
    exit /b 1
)

echo Starting Calculator MCP Server...
echo Profile: %PROFILE%
echo Port: %PORT%
echo Debug Mode: %DEBUG_MODE%
echo Java Options: %JAVA_OPTS%
echo App Options: %APP_OPTS%
echo.

REM 启动应用
java %JAVA_OPTS% -jar "%JAR_FILE%" %APP_OPTS%