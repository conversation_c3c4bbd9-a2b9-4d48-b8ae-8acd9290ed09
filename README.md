# Calculator MCP Server

一个基于Spring Boot和MCP SDK的简单计算器服务，提供基本的数学运算功能。该服务通过SSE（Server-Sent Events）实现MCP（Model Context Protocol）协议与Kiro IDE集成，支持加减乘除等基本计算操作。

## 功能特性

- ✅ 基本数学运算：加法、减法、乘法、除法
- ✅ 支持多个数字的运算
- ✅ 完整的输入验证和错误处理
- ✅ 响应式编程支持（Spring WebFlux）
- ✅ SSE（Server-Sent Events）传输支持
- ✅ MCP协议集成
- ✅ 健康检查和监控端点
- ✅ 并发请求处理
- ✅ Docker容器化支持
- ✅ 跨域支持（CORS）

## 技术栈

- **Java**: 21
- **Spring Boot**: 2.7.18
- **Spring WebFlux**: 响应式Web框架
- **MCP SDK**: 0.10.0
- **Maven**: 构建工具
- **Docker**: 容器化部署

## 快速开始

### 前置要求

- Java 21 或更高版本
- Maven 3.6 或更高版本
- Docker（可选，用于容器化部署）

### 本地开发

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd calcMcp
   ```

2. **编译项目**
   ```bash
   mvn clean compile
   ```

3. **运行测试**
   ```bash
   mvn test
   ```

4. **打包应用**
   ```bash
   mvn clean package
   ```

5. **启动应用**
   ```bash
   # 使用Maven
   mvn spring-boot:run
   
   # 或使用JAR文件
   java -jar target/calcMcp-0.0.1-SNAPSHOT.jar
   
   # 或使用启动脚本
   ./scripts/start.sh
   # Windows: scripts\start.bat
   ```

### Docker部署

1. **构建Docker镜像**
   ```bash
   docker build -t calculator-mcp-server .
   ```

2. **运行容器**
   ```bash
   docker run -p 8080:8080 calculator-mcp-server
   ```

3. **使用Docker Compose**
   ```bash
   docker-compose up -d
   ```

## API文档

### 基础端点

- **健康检查**: `GET /mcp/health`
- **服务器信息**: `GET /mcp/info`
- **SSE连接**: `GET /mcp/sse`
- **MCP消息处理**: `POST /mcp/message`

### MCP协议通信

#### SSE连接
```bash
curl -N -H "Accept: text/event-stream" http://localhost:8080/mcp/sse
```

#### MCP消息格式
发送到 `POST /mcp/message` 的请求格式：

```json
{
  "jsonrpc": "2.0",
  "id": 1,
  "method": "tools/call",
  "params": {
    "name": "calculator_add",
    "arguments": {
      "a": 10.0,
      "b": 5.0
    }
  }
}
```

#### 响应格式
```json
{
  "jsonrpc": "2.0",
  "id": 1,
  "result": {
    "content": [
      {
        "type": "text",
        "text": "10.00 + 5.00 = 15.00"
      }
    ]
  }
}
```

#### 可用工具

1. **加法** - `calculator_add`
   ```bash
   curl -X POST http://localhost:8080/mcp/message \
     -H "Content-Type: application/json" \
     -d '{
       "jsonrpc": "2.0",
       "id": 1,
       "method": "tools/call",
       "params": {
         "name": "calculator_add",
         "arguments": {"a": 10.0, "b": 5.0}
       }
     }'
   ```

2. **减法** - `calculator_subtract`
   ```bash
   curl -X POST http://localhost:8080/mcp/message \
     -H "Content-Type: application/json" \
     -d '{
       "jsonrpc": "2.0",
       "id": 2,
       "method": "tools/call",
       "params": {
         "name": "calculator_subtract",
         "arguments": {"a": 10.0, "b": 3.0}
       }
     }'
   ```

3. **乘法** - `calculator_multiply`
   ```bash
   curl -X POST http://localhost:8080/mcp/message \
     -H "Content-Type: application/json" \
     -d '{
       "jsonrpc": "2.0",
       "id": 3,
       "method": "tools/call",
       "params": {
         "name": "calculator_multiply",
         "arguments": {"a": 4.0, "b": 5.0}
       }
     }'
   ```

4. **除法** - `calculator_divide`
   ```bash
   curl -X POST http://localhost:8080/mcp/message \
     -H "Content-Type: application/json" \
     -d '{
       "jsonrpc": "2.0",
       "id": 4,
       "method": "tools/call",
       "params": {
         "name": "calculator_divide",
         "arguments": {"a": 20.0, "b": 2.0}
       }
     }'
   ```

## 第三方MCP客户端集成

### 在Kiro IDE中使用

1. **配置MCP服务器**

   在工作区创建或编辑 `.kiro/settings/mcp.json` 文件：
   ```json
   {
     "mcpServers": {
       "calculator": {
         "transport": {
           "type": "sse",
           "url": "http://localhost:8080/mcp/sse"
         },
         "env": {},
         "disabled": false,
         "autoApprove": ["calculator_add", "calculator_subtract", "calculator_multiply", "calculator_divide"]
       }
     }
   }
   ```

2. **使用计算工具**

   配置完成后，你可以在Kiro中直接使用计算功能：
   ```
   请帮我计算 15 + 25 + 10
   请计算 100 除以 5 再除以 2
   帮我算一下 3 * 4 * 5 的结果
   ```

### 在Claude Desktop中使用

1. **配置claude_desktop_config.json**

   编辑Claude Desktop配置文件（通常位于 `~/.claude/claude_desktop_config.json`）：
   ```json
   {
     "mcpServers": {
       "calculator": {
         "transport": {
           "type": "sse",
           "url": "http://localhost:8080/mcp/sse"
         }
       }
     }
   }
   ```

2. **重启Claude Desktop**

   配置完成后重启Claude Desktop应用程序。

### 在其他MCP客户端中使用

对于支持MCP协议的其他客户端，通常需要：

1. **启动服务器**
   ```bash
   java -jar target/calcMcp-0.0.1-SNAPSHOT.jar
   ```

2. **配置客户端**

   根据客户端的配置格式，添加MCP服务器配置：
   - **传输类型**: `sse`
   - **SSE URL**: `http://localhost:8080/mcp/sse`
   - **消息端点**: `http://localhost:8080/mcp/message`

3. **可用工具**

   配置成功后，客户端将可以使用以下工具：
   - `calculator_add`: 加法运算
   - `calculator_subtract`: 减法运算
   - `calculator_multiply`: 乘法运算
   - `calculator_divide`: 除法运算

### 验证连接

你可以通过以下方式验证MCP连接是否正常：

1. **检查服务器状态**
   ```bash
   curl http://localhost:8080/mcp/health
   ```

2. **列出可用工具**
   ```bash
   curl http://localhost:8080/mcp/tools
   ```

3. **测试计算功能**
   ```bash
   curl -X POST http://localhost:8080/mcp/message \
     -H "Content-Type: application/json" \
     -d '{
       "jsonrpc": "2.0",
       "id": 1,
       "method": "tools/call",
       "params": {
         "name": "calculator_add",
         "arguments": {"a": 1, "b": 2}
       }
     }'
   ```

### 故障排除

如果MCP客户端无法连接到计算器服务：

1. **检查服务是否运行**
   ```bash
   netstat -tulpn | grep 8080
   ```

2. **检查防火墙设置**
   确保端口8080没有被防火墙阻止

3. **查看服务日志**
   ```bash
   tail -f logs/calculator-mcp-server.log
   ```

4. **验证Java版本**
   ```bash
   java -version
   # 需要Java 21或更高版本
   ```

## 配置

### 应用配置

主要配置在 `src/main/resources/application.yml` 中：

```yaml
server:
  port: 8080

mcp:
  server:
    name: "Calculator MCP Server"
    version: "1.0.0"
    max-concurrent-requests: 100

logging:
  level:
    io.kt666.mcp: DEBUG
```

### 环境配置

支持多个环境配置：
- `dev`: 开发环境
- `test`: 测试环境  
- `prod`: 生产环境

使用 `--spring.profiles.active=<profile>` 指定环境。

### 启动参数

启动脚本支持以下参数：
- `--profile <profile>`: 设置活动配置文件
- `--port <port>`: 设置服务端口
- `--debug`: 启用调试模式

## 监控

### 健康检查

```bash
curl http://localhost:8080/mcp/health
```

### Actuator端点

- **健康状态**: `/actuator/health`
- **应用信息**: `/actuator/info`
- **指标数据**: `/actuator/metrics`
- **Prometheus指标**: `/actuator/prometheus`

## 开发

### 项目结构

```
src/main/java/io/kt666/mcp/calcmcp/
├── CalcMcpApplication.java          # 主应用类
├── config/
│   └── McpConfiguration.java       # MCP配置
├── controller/
│   ├── CalculatorController.java   # REST控制器
│   └── GlobalExceptionHandler.java # 全局异常处理
├── service/
│   ├── CalculatorService.java      # 计算服务接口
│   └── InputValidator.java         # 输入验证接口
├── service/impl/
│   ├── CalculatorServiceImpl.java  # 计算服务实现
│   └── InputValidatorImpl.java     # 输入验证实现
├── model/
│   ├── CalculationRequest.java     # 请求模型
│   ├── CalculationResponse.java    # 响应模型
│   └── CalculationError.java       # 错误模型
├── exception/
│   ├── CalculationException.java   # 基础异常
│   ├── InvalidInputException.java  # 输入异常
│   └── DivisionByZeroException.java # 除零异常
└── util/
    └── ResponseFormatter.java      # 响应格式化工具
```

### 运行测试

```bash
# 运行所有测试
mvn test

# 运行特定测试类
mvn test -Dtest=CalculatorServiceImplTest

# 运行集成测试
mvn test -Dtest=McpProtocolIntegrationTest

# 运行性能测试
mvn test -Dtest=ConcurrencyAndPerformanceTest
```

## 性能指标

- **响应时间**: 平均 < 100ms
- **并发处理**: 支持 100+ 并发请求
- **内存使用**: 启动后 < 512MB
- **错误恢复**: 单个请求错误不影响其他请求

## 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 检查端口使用情况
   netstat -tulpn | grep 8080
   # 或使用不同端口启动
   ./scripts/start.sh --port 8081
   ```

2. **Java版本不兼容**
   ```bash
   # 检查Java版本
   java -version
   # 需要Java 21或更高版本
   ```

3. **内存不足**
   ```bash
   # 调整JVM内存设置
   export JAVA_OPTS="-Xms512m -Xmx1024m"
   ```

### 日志

- **应用日志**: `logs/calculator-mcp-server.log`
- **控制台日志**: 实时输出到控制台
- **日志级别**: 可通过配置文件调整

## 贡献

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

- 项目链接: [https://github.com/your-username/calcMcp](https://github.com/your-username/calcMcp)
- 问题报告: [https://github.com/your-username/calcMcp/issues](https://github.com/your-username/calcMcp/issues)