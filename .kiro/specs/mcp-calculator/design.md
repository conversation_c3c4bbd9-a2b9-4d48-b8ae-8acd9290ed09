# 设计文档

## 概述

MCP计算器服务是一个基于Spring Boot和MCP SDK的微服务，提供基本的数学运算功能。该服务使用Spring WebFlux实现响应式编程，通过MCP协议与Kiro IDE集成，为用户提供加减乘除等基本计算功能。

服务采用Java 21和Spring Boot 2.7.18构建，使用MCP SDK 0.10.0实现协议支持。

## 架构

### 整体架构

```mermaid
graph TB
    A[Kiro IDE] -->|MCP Protocol| B[MCP Calculator Service]
    B --> C[Calculator Controller]
    C --> D[Calculator Service]
    D --> E[Math Operations]
    
    subgraph "MCP Calculator Service"
        C
        D
        E
        F[Error Handler]
        G[Input Validator]
    end
    
    D --> F
    C --> G
```

### 技术栈

- **框架**: Spring Boot 2.7.18 with WebFlux
- **Java版本**: Java 21
- **MCP SDK**: 0.10.0
- **构建工具**: Maven
- **响应式编程**: Project Reactor

## 组件和接口

### 1. MCP工具定义

服务将注册以下MCP工具：

#### 加法工具 (add)
- **名称**: `calculator_add`
- **描述**: 执行两个或多个数字的加法运算
- **参数**:
  - `numbers`: 数字数组，至少包含2个元素
- **返回**: 计算结果和操作描述

#### 减法工具 (subtract)
- **名称**: `calculator_subtract`
- **描述**: 执行数字的减法运算
- **参数**:
  - `numbers`: 数字数组，至少包含2个元素
- **返回**: 计算结果和操作描述

#### 乘法工具 (multiply)
- **名称**: `calculator_multiply`
- **描述**: 执行两个或多个数字的乘法运算
- **参数**:
  - `numbers`: 数字数组，至少包含2个元素
- **返回**: 计算结果和操作描述

#### 除法工具 (divide)
- **名称**: `calculator_divide`
- **描述**: 执行数字的除法运算
- **参数**:
  - `numbers`: 数字数组，至少包含2个元素
- **返回**: 计算结果和操作描述

### 2. 核心组件

#### CalculatorController
- **职责**: 处理MCP工具调用请求
- **方法**:
  - `handleToolCall(ToolCallRequest request)`: 路由工具调用到相应的服务方法
  - `listTools()`: 返回可用工具列表

#### CalculatorService (接口层 - service包)
- **职责**: 定义数学运算的服务接口
- **方法**:
  - `add(List<Double> numbers)`: 加法运算
  - `subtract(List<Double> numbers)`: 减法运算
  - `multiply(List<Double> numbers)`: 乘法运算
  - `divide(List<Double> numbers)`: 除法运算

#### CalculatorServiceImpl (实现层 - service.impl包)
- **职责**: 实现具体的数学运算逻辑
- **实现**: CalculatorService接口的具体实现

#### InputValidator (接口层 - service包)
- **职责**: 定义输入验证的服务接口
- **方法**:
  - `validateNumbers(List<Double> numbers)`: 验证数字列表
  - `validateNotEmpty(List<Double> numbers)`: 验证列表不为空
  - `validateDivisionByZero(List<Double> numbers)`: 验证除法中的除数不为零

#### InputValidatorImpl (实现层 - service.impl包)
- **职责**: 实现具体的输入验证逻辑
- **实现**: InputValidator接口的具体实现

#### McpConfiguration
- **职责**: 配置MCP服务和工具注册
- **方法**:
  - `mcpServer()`: 配置MCP服务器
  - `toolRegistry()`: 注册计算器工具

## 数据模型

### CalculationRequest
```java
public class CalculationRequest {
    private String operation;
    private List<Double> numbers;
    // getters and setters
}
```

### CalculationResponse
```java
public class CalculationResponse {
    private Double result;
    private String operation;
    private List<Double> operands;
    private String description;
    // getters and setters
}
```

### CalculationError
```java
public class CalculationError {
    private String errorCode;
    private String message;
    private String operation;
    // getters and setters
}
```

## 错误处理

### 错误类型和处理策略

1. **输入验证错误**
   - 空参数列表
   - 非数字输入
   - 参数数量不足

2. **数学运算错误**
   - 除零错误
   - 数值溢出
   - 无穷大结果

3. **MCP协议错误**
   - 工具未找到
   - 参数格式错误
   - 协议版本不匹配

### 错误响应格式

所有错误将按照MCP协议标准格式返回：

```json
{
  "error": {
    "code": "CALCULATION_ERROR",
    "message": "Division by zero is not allowed",
    "data": {
      "operation": "divide",
      "operands": [10.0, 0.0]
    }
  }
}
```

## 测试策略

### 单元测试

1. **CalculatorServiceImpl测试**
   - 测试各种数学运算的正确性
   - 测试边界条件和异常情况
   - 测试精度和舍入行为

2. **InputValidatorImpl测试**
   - 测试各种输入验证场景
   - 测试错误消息的准确性

3. **Controller测试**
   - 测试MCP工具调用的路由
   - 测试请求和响应的序列化

### 集成测试

1. **MCP协议集成测试**
   - 测试工具注册和发现
   - 测试完整的请求-响应流程
   - 测试错误处理和异常情况

2. **端到端测试**
   - 模拟Kiro IDE的调用
   - 测试并发请求处理
   - 测试服务启动和关闭

### 测试数据

- 正常数值：整数、小数、负数
- 边界值：零、最大值、最小值
- 异常情况：NaN、无穷大、null值

## 性能考虑

1. **响应时间**: 单次计算操作应在10ms内完成
2. **并发处理**: 支持至少100个并发请求
3. **内存使用**: 服务启动后内存占用不超过128MB
4. **错误恢复**: 单个请求错误不应影响其他请求的处理

## 部署和配置

### 应用配置

```yaml
server:
  port: 8080

mcp:
  server:
    name: "Calculator MCP Server"
    version: "1.0.0"
    
logging:
  level:
    io.kt666.mcp: DEBUG
    io.modelcontextprotocol: INFO
```

### 启动脚本

服务将提供标准的Spring Boot启动方式，支持通过命令行参数配置端口和其他选项。